const mongoose = require('mongoose');

const noticeSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  content: {
    type: String,
    required: true
  },
  category: {
    type: String,
    required: true,
    enum: [
      'promotion',
      'transfer notice',
      'directives',
      'rules',
      'exams',
      'order',
      'general notice',
      'law',
      'un notice',
      'deputation',
      'other notice(career)',
      'bipad notice',
      'public procurement',
      'ordinance',
      'procedure'
    ]
  },
  province: {
    type: String,
    default: 'All Provinces',
    enum: [
      'All Provinces',
      'Province 1',
      'Madhesh Province',
      'Bagmati Province',
      'Gandaki Province',
      'Lumbini Province',
      'Karnali Province',
      'Sudurpashchim Province'
    ]
  },
  status: {
    type: String,
    enum: ['draft', 'published'],
    default: 'published'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  image: {
    filename: String,
    originalName: String,
    url: String,
    size: Number
  },
  views: {
    type: Number,
    default: 0
  },
  likes: {
    type: Number,
    default: 0
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  publishedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Index for better query performance
noticeSchema.index({ category: 1, province: 1, status: 1 });
noticeSchema.index({ createdAt: -1 });
noticeSchema.index({ title: 'text', content: 'text' });

module.exports = mongoose.model('Notice', noticeSchema);
