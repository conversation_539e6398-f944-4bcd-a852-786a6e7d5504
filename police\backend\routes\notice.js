const express = require('express');
const router = express.Router();
const Notice = require('../models/Notice');
const auth = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = 'uploads/notice';
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'notice-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: function (req, file, cb) {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Get all notices with filtering
router.get('/', async (req, res) => {
  try {
    const { category, province, search, page = 1, limit = 10 } = req.query;
    
    let query = { status: 'published' };
    
    // Add category filter
    if (category && category !== 'all') {
      query.category = category;
    }
    
    // Add province filter
    if (province && province !== 'All Provinces') {
      query.province = province;
    }
    
    // Add search filter
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } }
      ];
    }
    
    const skip = (page - 1) * limit;
    
    const notices = await Notice.find(query)
      .populate('createdBy', 'name email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    const total = await Notice.countDocuments(query);
    
    res.json({
      notices,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Error fetching notices:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get single notice by ID
router.get('/:id', async (req, res) => {
  try {
    const notice = await Notice.findById(req.params.id)
      .populate('createdBy', 'name email');
    
    if (!notice) {
      return res.status(404).json({ message: 'Notice not found' });
    }
    
    // Increment views
    notice.views += 1;
    await notice.save();
    
    res.json(notice);
  } catch (error) {
    console.error('Error fetching notice:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Create new notice (Admin only)
router.post('/', auth, upload.single('image'), async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    const { title, content, category, province, priority } = req.body;
    
    const noticeData = {
      title,
      content,
      category,
      province: province || 'All Provinces',
      priority: priority || 'medium',
      createdBy: req.user.id
    };
    
    // Add image if uploaded
    if (req.file) {
      noticeData.image = {
        filename: req.file.filename,
        originalName: req.file.originalname,
        url: `/uploads/notice/${req.file.filename}`,
        size: req.file.size
      };
    }
    
    const notice = new Notice(noticeData);
    await notice.save();
    
    await notice.populate('createdBy', 'name email');
    
    res.status(201).json(notice);
  } catch (error) {
    console.error('Error creating notice:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update notice (Admin only)
router.put('/:id', auth, upload.single('image'), async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    const notice = await Notice.findById(req.params.id);
    if (!notice) {
      return res.status(404).json({ message: 'Notice not found' });
    }

    const { title, content, category, province, priority } = req.body;
    
    // Update fields
    if (title) notice.title = title;
    if (content) notice.content = content;
    if (category) notice.category = category;
    if (province) notice.province = province;
    if (priority) notice.priority = priority;
    
    // Update image if new one uploaded
    if (req.file) {
      // Delete old image if exists
      if (notice.image && notice.image.filename) {
        const oldImagePath = path.join(__dirname, '../uploads/notice', notice.image.filename);
        if (fs.existsSync(oldImagePath)) {
          fs.unlinkSync(oldImagePath);
        }
      }
      
      notice.image = {
        filename: req.file.filename,
        originalName: req.file.originalname,
        url: `/uploads/notice/${req.file.filename}`,
        size: req.file.size
      };
    }
    
    await notice.save();
    await notice.populate('createdBy', 'name email');
    
    res.json(notice);
  } catch (error) {
    console.error('Error updating notice:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Delete notice (Admin only)
router.delete('/:id', auth, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    const notice = await Notice.findById(req.params.id);
    if (!notice) {
      return res.status(404).json({ message: 'Notice not found' });
    }

    // Delete image file if exists
    if (notice.image && notice.image.filename) {
      const imagePath = path.join(__dirname, '../uploads/notice', notice.image.filename);
      if (fs.existsSync(imagePath)) {
        fs.unlinkSync(imagePath);
      }
    }

    await Notice.findByIdAndDelete(req.params.id);
    res.json({ message: 'Notice deleted successfully' });
  } catch (error) {
    console.error('Error deleting notice:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Like notice
router.post('/:id/like', auth, async (req, res) => {
  try {
    const notice = await Notice.findById(req.params.id);
    if (!notice) {
      return res.status(404).json({ message: 'Notice not found' });
    }
    
    notice.likes += 1;
    await notice.save();
    
    res.json({ likes: notice.likes });
  } catch (error) {
    console.error('Error liking notice:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
