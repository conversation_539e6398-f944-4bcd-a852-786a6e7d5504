const mongoose = require('mongoose');
const Notice = require('../models/Notice');
const User = require('../models/User');
require('dotenv').config();

const sampleNotices = [
  {
    title: 'बाटो अवरोध रुन्ने (२०८२-०२-२१)',
    content: 'सबै नागरिकहरूलाई सूचना दिइन्छ कि आगामी मितिमा सडक मर्मत कार्यका कारण यातायात अवरुद्ध हुनेछ। कृपया वैकल्पिक मार्ग प्रयोग गर्नुहोस्।',
    category: 'general notice',
    province: 'Bagmati Province',
    priority: 'high'
  },
  {
    title: 'जनपद प्रहरी समूहतर्फ प्रहरी अतिरिक्त महानिरीक्षक पदमा बढुवा नियुक्ति तथा पदस्थापन गरिएको (मिति २०८२।०२।१६ गते)',
    content: 'नेपाल प्रहरीको जनपद प्रहरी समूहतर्फ प्रहरी अतिरिक्त महानिरीक्षक पदमा बढुवा नियुक्ति तथा पदस्थापन गरिएको सम्बन्धमा यो सूचना प्रकाशित गरिएको छ।',
    category: 'promotion',
    province: 'All Provinces',
    priority: 'medium'
  },
  {
    title: 'जनपद प्रहरी समूहतर्फ प्रहरी नायब महानिरीक्षक पदमा बढुवा नियुक्ति तथा पदस्थापन गरिएको (मिति २०८२।०२।१६ गते)',
    content: 'नेपाल प्रहरीको जनपद प्रहरी समूहतर्फ प्रहरी नायब महानिरीक्षक पदमा बढुवा नियुक्ति तथा पदस्थापन गरिएको सम्बन्धमा यो सूचना प्रकाशित गरिएको छ।',
    category: 'promotion',
    province: 'All Provinces',
    priority: 'medium'
  },
  {
    title: 'प्राविधिक प्रहरी समूह, इन्जिनियरिङ उपसमूह, सूचना प्रविधि/कम्प्युटरतर्फ प्रहरी सहायक निरीक्षक पदमा बढुवा सिफारिसको सूची प्रकाशित गरिएको',
    content: 'प्राविधिक प्रहरी समूह, इन्जिनियरिङ उपसमूह, सूचना प्रविधि/कम्प्युटरतर्फ प्रहरी सहायक निरीक्षक पदमा बढुवा सिफारिसको सूची प्रकाशित गरिएको छ।',
    category: 'promotion',
    province: 'All Provinces',
    priority: 'medium'
  },
  {
    title: 'जनपद प्रहरी समूहतर्फ प्रहरी हरिष हमालदार पदमा बढुवा सिफारिस सूची प्रकाशित गरिएको (मिति २०८२।०२।०९ गते)',
    content: 'जनपद प्रहरी समूहतर्फ प्रहरी हरिष हमालदार पदमा बढुवा सिफारिस सूची प्रकाशित गरिएको सम्बन्धमा यो सूचना जारी गरिएको छ।',
    category: 'promotion',
    province: 'All Provinces',
    priority: 'medium'
  },
  {
    title: 'सम्पत्ति तथा जिन्सी मालसामानको लिलाम बिक्री सम्बन्धी बोलपत्र आह्वानको सूचना',
    content: 'नेपाल प्रहरीको सम्पत्ति तथा जिन्सी मालसामानको लिलाम बिक्री सम्बन्धमा बोलपत्र आह्वान गरिएको छ। इच्छुक व्यक्तिहरूले निर्धारित मितिभित्र आवेदन दिन सक्नेछन्।',
    category: 'public procurement',
    province: 'All Provinces',
    priority: 'low'
  },
  {
    title: 'प्रहरी भर्ना परीक्षा सम्बन्धी सूचना',
    content: 'नेपाल प्रहरीमा विभिन्न पदहरूमा भर्ना परीक्षा सञ्चालन गरिने भएको छ। इच्छुक उम्मेदवारहरूले आवेदन दिन सक्नेछन्।',
    category: 'exams',
    province: 'All Provinces',
    priority: 'high'
  },
  {
    title: 'ट्राफिक नियम सम्बन्धी नयाँ निर्देशिका',
    content: 'सडक ट्राफिक व्यवस्थापनका लागि नयाँ निर्देशिका जारी गरिएको छ। सबै चालकहरूले यसको पालना गर्नुपर्नेछ।',
    category: 'directives',
    province: 'All Provinces',
    priority: 'medium'
  },
  {
    title: 'साइबर अपराध सम्बन्धी जनचेतना कार्यक्रम',
    content: 'साइबर अपराधबाट बच्नका लागि जनचेतना कार्यक्रम सञ्चालन गरिने भएको छ। सबै नागरिकहरूको सहभागिता अपेक्षा गरिएको छ।',
    category: 'general notice',
    province: 'Bagmati Province',
    priority: 'medium'
  },
  {
    title: 'बिपद् व्यवस्थापन सम्बन्धी तालिम कार्यक्रम',
    content: 'प्राकृतिक बिपद्का समयमा गर्नुपर्ने कार्यहरू सम्बन्धमा तालिम कार्यक्रम आयोजना गरिने भएको छ।',
    category: 'bipad notice',
    province: 'All Provinces',
    priority: 'high'
  }
];

async function createSampleNotices() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/police_db';
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    // Find admin user
    const adminUser = await User.findOne({ role: 'admin' });
    if (!adminUser) {
      console.log('No admin user found. Please create an admin user first.');
      process.exit(1);
    }

    // Clear existing notices
    await Notice.deleteMany({});
    console.log('Cleared existing notices');

    // Create sample notices
    const noticesWithAdmin = sampleNotices.map(notice => ({
      ...notice,
      createdBy: adminUser._id,
      publishedAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // Random date within last 30 days
    }));

    const createdNotices = await Notice.insertMany(noticesWithAdmin);
    console.log(`Created ${createdNotices.length} sample notices`);

    // Add some random views and likes
    for (const notice of createdNotices) {
      notice.views = Math.floor(Math.random() * 500) + 10;
      notice.likes = Math.floor(Math.random() * 50) + 1;
      await notice.save();
    }

    console.log('Added random views and likes to notices');
    console.log('Sample notices created successfully!');

  } catch (error) {
    console.error('Error creating sample notices:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
createSampleNotices();
