[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\login.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\signup.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Login.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Signup.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\data\\nepal-data.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\ForgotPassword.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Home.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\ReportIncident.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\IncidentDetails.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\services\\api.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\LocationPicker.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\context\\AuthContext.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\MyIncidents.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\DateRangePicker.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\EmergencyContact.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\BloodBank.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\BloodBankList.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Hospital.js": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Ambulance.js": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Police.js": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\FireBrigade.js": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\News.js": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\NewsDetail.js": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\AdminNews.js": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\FilterModal.js": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\NoticeDetail.js": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\AdminNotice.js": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Notice.js": "31"}, {"size": 525, "mtime": 1748896251719, "results": "32", "hashOfConfig": "33"}, {"size": 5288, "mtime": 1749068002338, "results": "34", "hashOfConfig": "33"}, {"size": 362, "mtime": 1742330183325, "results": "35", "hashOfConfig": "33"}, {"size": 1146, "mtime": 1742331724908, "results": "36", "hashOfConfig": "33"}, {"size": 748, "mtime": 1742331958204, "results": "37", "hashOfConfig": "33"}, {"size": 3879, "mtime": 1748627041579, "results": "38", "hashOfConfig": "33"}, {"size": 10293, "mtime": 1748634724820, "results": "39", "hashOfConfig": "33"}, {"size": 17018, "mtime": 1743931339665, "results": "40", "hashOfConfig": "33"}, {"size": 2290, "mtime": 1743713070038, "results": "41", "hashOfConfig": "33"}, {"size": 4436, "mtime": 1749068083728, "results": "42", "hashOfConfig": "33"}, {"size": 5857, "mtime": 1748629829926, "results": "43", "hashOfConfig": "33"}, {"size": 16259, "mtime": 1748634676403, "results": "44", "hashOfConfig": "33"}, {"size": 2646, "mtime": 1748631658121, "results": "45", "hashOfConfig": "33"}, {"size": 7598, "mtime": 1748634806469, "results": "46", "hashOfConfig": "33"}, {"size": 2343, "mtime": 1748634742641, "results": "47", "hashOfConfig": "33"}, {"size": 13183, "mtime": 1748807727692, "results": "48", "hashOfConfig": "33"}, {"size": 6768, "mtime": 1748806783103, "results": "49", "hashOfConfig": "33"}, {"size": 3068, "mtime": 1748900664607, "results": "50", "hashOfConfig": "33"}, {"size": 14674, "mtime": 1748896141483, "results": "51", "hashOfConfig": "33"}, {"size": 8276, "mtime": 1748900199864, "results": "52", "hashOfConfig": "33"}, {"size": 14397, "mtime": 1748900213484, "results": "53", "hashOfConfig": "33"}, {"size": 12193, "mtime": 1748900510896, "results": "54", "hashOfConfig": "33"}, {"size": 12153, "mtime": 1748900609635, "results": "55", "hashOfConfig": "33"}, {"size": 11165, "mtime": 1748900408015, "results": "56", "hashOfConfig": "33"}, {"size": 9502, "mtime": 1748989137151, "results": "57", "hashOfConfig": "33"}, {"size": 6742, "mtime": 1748989187874, "results": "58", "hashOfConfig": "33"}, {"size": 16136, "mtime": 1748985209065, "results": "59", "hashOfConfig": "33"}, {"size": 2509, "mtime": 1748989223750, "results": "60", "hashOfConfig": "33"}, {"size": 6917, "mtime": 1749067848176, "results": "61", "hashOfConfig": "33"}, {"size": 13946, "mtime": 1749067900340, "results": "62", "hashOfConfig": "33"}, {"size": 9392, "mtime": 1749067735533, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "5zg9je", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\login.js", ["157", "158"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\signup.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Login.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Signup.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\data\\nepal-data.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\ForgotPassword.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Home.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\ReportIncident.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\IncidentDetails.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\LocationPicker.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\MyIncidents.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\DateRangePicker.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\EmergencyContact.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\BloodBank.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\BloodBankList.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Hospital.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Ambulance.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Police.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\FireBrigade.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\News.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\NewsDetail.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\AdminNews.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\FilterModal.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\NoticeDetail.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\AdminNotice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Notice.js", [], [], {"ruleId": "159", "severity": 1, "message": "160", "line": 23, "column": 11, "nodeType": "161", "endLine": 23, "endColumn": 23}, {"ruleId": "159", "severity": 1, "message": "160", "line": 27, "column": 38, "nodeType": "161", "endLine": 27, "endColumn": 50}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement"]