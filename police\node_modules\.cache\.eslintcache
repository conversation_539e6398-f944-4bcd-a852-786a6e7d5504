[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\login.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\signup.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Login.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Signup.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\data\\nepal-data.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\ForgotPassword.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Home.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\ReportIncident.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\IncidentDetails.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\services\\api.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\LocationPicker.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\context\\AuthContext.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\MyIncidents.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\DateRangePicker.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\EmergencyContact.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\BloodBank.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\BloodBankList.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Hospital.js": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Ambulance.js": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Police.js": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\FireBrigade.js": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\News.js": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\NewsDetail.js": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\AdminNews.js": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\FilterModal.js": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\NoticeDetail.js": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\AdminNotice.js": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Notice.js": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Settings.js": "32"}, {"size": 525, "mtime": 1748896251719, "results": "33", "hashOfConfig": "34"}, {"size": 5495, "mtime": 1749069751167, "results": "35", "hashOfConfig": "34"}, {"size": 362, "mtime": 1742330183325, "results": "36", "hashOfConfig": "34"}, {"size": 1146, "mtime": 1742331724908, "results": "37", "hashOfConfig": "34"}, {"size": 748, "mtime": 1742331958204, "results": "38", "hashOfConfig": "34"}, {"size": 3879, "mtime": 1748627041579, "results": "39", "hashOfConfig": "34"}, {"size": 10293, "mtime": 1748634724820, "results": "40", "hashOfConfig": "34"}, {"size": 17018, "mtime": 1743931339665, "results": "41", "hashOfConfig": "34"}, {"size": 2290, "mtime": 1743713070038, "results": "42", "hashOfConfig": "34"}, {"size": 4474, "mtime": 1749069768739, "results": "43", "hashOfConfig": "34"}, {"size": 5857, "mtime": 1748629829926, "results": "44", "hashOfConfig": "34"}, {"size": 16259, "mtime": 1748634676403, "results": "45", "hashOfConfig": "34"}, {"size": 2646, "mtime": 1748631658121, "results": "46", "hashOfConfig": "34"}, {"size": 7598, "mtime": 1748634806469, "results": "47", "hashOfConfig": "34"}, {"size": 2343, "mtime": 1748634742641, "results": "48", "hashOfConfig": "34"}, {"size": 13183, "mtime": 1748807727692, "results": "49", "hashOfConfig": "34"}, {"size": 6768, "mtime": 1748806783103, "results": "50", "hashOfConfig": "34"}, {"size": 3068, "mtime": 1748900664607, "results": "51", "hashOfConfig": "34"}, {"size": 14674, "mtime": 1748896141483, "results": "52", "hashOfConfig": "34"}, {"size": 8276, "mtime": 1748900199864, "results": "53", "hashOfConfig": "34"}, {"size": 14397, "mtime": 1748900213484, "results": "54", "hashOfConfig": "34"}, {"size": 12193, "mtime": 1748900510896, "results": "55", "hashOfConfig": "34"}, {"size": 12153, "mtime": 1748900609635, "results": "56", "hashOfConfig": "34"}, {"size": 11165, "mtime": 1748900408015, "results": "57", "hashOfConfig": "34"}, {"size": 9640, "mtime": 1749069148515, "results": "58", "hashOfConfig": "34"}, {"size": 6742, "mtime": 1748989187874, "results": "59", "hashOfConfig": "34"}, {"size": 16136, "mtime": 1748985209065, "results": "60", "hashOfConfig": "34"}, {"size": 2509, "mtime": 1748989223750, "results": "61", "hashOfConfig": "34"}, {"size": 6917, "mtime": 1749067848176, "results": "62", "hashOfConfig": "34"}, {"size": 14432, "mtime": 1749068524930, "results": "63", "hashOfConfig": "34"}, {"size": 9534, "mtime": 1749069117700, "results": "64", "hashOfConfig": "34"}, {"size": 9210, "mtime": 1749069689175, "results": "65", "hashOfConfig": "34"}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "5zg9je", {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\login.js", ["162", "163"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\signup.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Login.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Signup.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\data\\nepal-data.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\ForgotPassword.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Home.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\ReportIncident.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\IncidentDetails.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\LocationPicker.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\MyIncidents.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\DateRangePicker.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\EmergencyContact.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\BloodBank.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\BloodBankList.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Hospital.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Ambulance.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Police.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\FireBrigade.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\News.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\NewsDetail.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\AdminNews.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\FilterModal.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\NoticeDetail.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\AdminNotice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Notice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\POlice\\police\\src\\component\\Settings.js", ["164"], [], {"ruleId": "165", "severity": 1, "message": "166", "line": 23, "column": 11, "nodeType": "167", "endLine": 23, "endColumn": 23}, {"ruleId": "165", "severity": 1, "message": "166", "line": 27, "column": 38, "nodeType": "167", "endLine": 27, "endColumn": 50}, {"ruleId": "168", "severity": 1, "message": "169", "line": 16, "column": 28, "nodeType": "170", "messageId": "171", "endLine": 16, "endColumn": 47}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-unused-vars", "'setSelectedLanguage' is assigned a value but never used.", "Identifier", "unusedVar"]