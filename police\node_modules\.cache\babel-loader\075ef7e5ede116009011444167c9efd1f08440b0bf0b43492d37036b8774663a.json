{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\POlice\\\\police\\\\src\\\\component\\\\Notice.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport axios from 'axios';\nimport FilterModal from './FilterModal';\nimport './Notice.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Notice = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useAuth();\n  const [notices, setNotices] = useState([]);\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedProvince, setSelectedProvince] = useState('All Provinces');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [showFilterModal, setShowFilterModal] = useState(false);\n  const noticeCategories = ['all', 'promotion', 'transfer notice', 'directives', 'rules', 'exams', 'order', 'general notice', 'law', 'un notice', 'deputation', 'other notice(career)', 'bipad notice', 'public procurement', 'ordinance', 'procedure'];\n  const provinces = ['All Provinces', 'Province 1', 'Madhesh Province', 'Bagmati Province', 'Gandaki Province', 'Lumbini Province', 'Karnali Province', 'Sudurpashchim Province'];\n  const fetchNotices = useCallback(async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      if (selectedCategory !== 'all') {\n        params.append('category', selectedCategory);\n      }\n      if (selectedProvince !== 'All Provinces') {\n        params.append('province', selectedProvince);\n      }\n      if (searchQuery.trim()) {\n        params.append('search', searchQuery.trim());\n      }\n      const response = await axios.get(`http://localhost:5000/api/notice?${params.toString()}`, {\n        withCredentials: true\n      });\n      setNotices(response.data.notices || []);\n    } catch (err) {\n      setError('Failed to fetch notices');\n      console.error('Error fetching notices:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedCategory, selectedProvince, searchQuery]);\n  useEffect(() => {\n    fetchNotices();\n  }, [fetchNotices]);\n  const handleBack = () => {\n    navigate('/home');\n  };\n  const handleCategorySelect = category => {\n    setSelectedCategory(category);\n  };\n  const handleProvinceChange = e => {\n    setSelectedProvince(e.target.value);\n  };\n  const handleSearchChange = e => {\n    setSearchQuery(e.target.value);\n  };\n  const handleFilterClick = () => {\n    setShowFilterModal(true);\n  };\n  const handleFilterClose = () => {\n    setShowFilterModal(false);\n  };\n  const handleFilterApply = () => {\n    // Filter will be applied automatically due to useEffect dependencies\n    setShowFilterModal(false);\n  };\n  const handleNoticeClick = noticeId => {\n    navigate(`/notice/${noticeId}`);\n  };\n  const handleAdminPanel = () => {\n    navigate('/admin/notice');\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n\n    // Simple B.S. conversion (approximate)\n    const bsYear = year + 57;\n    return `${year}-${month}-${day} (${bsYear}-${month}-${day} B.S)`;\n  };\n  const getCategoryDisplayName = category => {\n    if (category === 'all') return 'All';\n    return category.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');\n  };\n\n  // Show all categories\n  const visibleCategories = noticeCategories;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"notice-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"notice-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notice-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"notice-back-btn\",\n          onClick: handleBack,\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"24\",\n            height: \"24\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\",\n              fill: \"white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Notice\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notice-header-actions\",\n          children: [user && user.role === 'admin' && /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn\",\n            onClick: handleAdminPanel,\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"white\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"filter-btn\",\n            onClick: handleFilterClick,\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"white\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M4.25 5.61C6.27 8.2 10 13 10 13v6c0 .55.45 1 1 1h2c.55 0 1-.45 1-1v-6s3.73-4.8 5.75-7.39C20.25 4.95 19.78 4 18.95 4H5.04c-.83 0-1.3.95-.79 1.61z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notice-search-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notice-search-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search\",\n            value: searchQuery,\n            onChange: handleSearchChange,\n            className: \"notice-search-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"notice-search-icon\",\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notice-province-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedProvince,\n          onChange: handleProvinceChange,\n          className: \"notice-province-select\",\n          children: provinces.map(province => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: province,\n            children: province\n          }, province, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"province-dropdown-icon\",\n          width: \"20\",\n          height: \"20\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M7 10l5 5 5-5z\",\n            fill: \"currentColor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notice-categories-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notice-categories-scroll\",\n          children: visibleCategories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `category-btn ${selectedCategory === category ? 'active' : ''}`,\n            onClick: () => handleCategorySelect(category),\n            children: getCategoryDisplayName(category)\n          }, category, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notice-content\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notice-loading\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading notices...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notice-error\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: fetchNotices,\n            className: \"retry-btn\",\n            children: \"Retry\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this) : notices.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notice-empty\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No notices found for the selected criteria.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notice-list\",\n          children: notices.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notice-item\",\n            onClick: () => handleNoticeClick(item._id),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"notice-item-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"notice-item-date\",\n                children: formatDate(item.createdAt)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"notice-item-title\",\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 21\n              }, this), item.content && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"notice-item-preview\",\n                children: [item.content.substring(0, 100), \"...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"notice-item-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"notice-category\",\n                  children: getCategoryDisplayName(item.category)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 23\n                }, this), item.province !== 'All Provinces' && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"notice-province\",\n                  children: item.province\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 19\n            }, this), item.image && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"notice-item-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: `http://localhost:5000${item.image.url}`,\n                alt: item.title,\n                onError: e => {\n                  e.target.style.display = 'none';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 21\n            }, this)]\n          }, item._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FilterModal, {\n      isOpen: showFilterModal,\n      onClose: handleFilterClose,\n      selectedCategory: selectedCategory,\n      selectedProvince: selectedProvince,\n      onCategoryChange: setSelectedCategory,\n      onProvinceChange: setSelectedProvince,\n      onApply: handleFilterApply\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this);\n};\n_s(Notice, \"6TcoGY7bSBGo7SBrWTrwFE280Hg=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Notice;\nexport default Notice;\nvar _c;\n$RefreshReg$(_c, \"Notice\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useNavigate", "useAuth", "axios", "FilterModal", "jsxDEV", "_jsxDEV", "Notice", "_s", "navigate", "user", "notices", "setNotices", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedProvince", "searchQuery", "setSearch<PERSON>uery", "loading", "setLoading", "error", "setError", "showFilterModal", "setShowFilterModal", "noticeCategories", "provinces", "fetchNotices", "params", "URLSearchParams", "append", "trim", "response", "get", "toString", "withCredentials", "data", "err", "console", "handleBack", "handleCategorySelect", "category", "handleProvinceChange", "e", "target", "value", "handleSearchChange", "handleFilterClick", "handleFilterClose", "handleFilterApply", "handleNoticeClick", "noticeId", "handleAdminPanel", "formatDate", "dateString", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "bsYear", "getCategoryDisplayName", "split", "map", "word", "char<PERSON>t", "toUpperCase", "slice", "join", "visibleCategories", "className", "children", "onClick", "width", "height", "viewBox", "d", "fill", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "type", "placeholder", "onChange", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "province", "length", "item", "_id", "createdAt", "title", "content", "substring", "image", "src", "url", "alt", "onError", "style", "display", "isOpen", "onClose", "onCategoryChange", "onProvinceChange", "onApply", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/POlice/police/src/component/Notice.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport axios from 'axios';\nimport FilterModal from './FilterModal';\nimport './Notice.css';\n\nconst Notice = () => {\n  const navigate = useNavigate();\n  const { user } = useAuth();\n  const [notices, setNotices] = useState([]);\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedProvince, setSelectedProvince] = useState('All Provinces');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [showFilterModal, setShowFilterModal] = useState(false);\n\n  const noticeCategories = [\n    'all',\n    'promotion',\n    'transfer notice',\n    'directives',\n    'rules',\n    'exams',\n    'order',\n    'general notice',\n    'law',\n    'un notice',\n    'deputation',\n    'other notice(career)',\n    'bipad notice',\n    'public procurement',\n    'ordinance',\n    'procedure'\n  ];\n\n  const provinces = [\n    'All Provinces',\n    'Province 1',\n    'Madhesh Province',\n    'Bagmati Province',\n    'Gandaki Province',\n    'Lumbini Province',\n    'Karnali Province',\n    'Sudurpashchim Province'\n  ];\n\n  const fetchNotices = useCallback(async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      \n      if (selectedCategory !== 'all') {\n        params.append('category', selectedCategory);\n      }\n      \n      if (selectedProvince !== 'All Provinces') {\n        params.append('province', selectedProvince);\n      }\n      \n      if (searchQuery.trim()) {\n        params.append('search', searchQuery.trim());\n      }\n      \n      const response = await axios.get(`http://localhost:5000/api/notice?${params.toString()}`, {\n        withCredentials: true\n      });\n      \n      setNotices(response.data.notices || []);\n    } catch (err) {\n      setError('Failed to fetch notices');\n      console.error('Error fetching notices:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedCategory, selectedProvince, searchQuery]);\n\n  useEffect(() => {\n    fetchNotices();\n  }, [fetchNotices]);\n\n  const handleBack = () => {\n    navigate('/home');\n  };\n\n  const handleCategorySelect = (category) => {\n    setSelectedCategory(category);\n  };\n\n  const handleProvinceChange = (e) => {\n    setSelectedProvince(e.target.value);\n  };\n\n  const handleSearchChange = (e) => {\n    setSearchQuery(e.target.value);\n  };\n\n  const handleFilterClick = () => {\n    setShowFilterModal(true);\n  };\n\n  const handleFilterClose = () => {\n    setShowFilterModal(false);\n  };\n\n  const handleFilterApply = () => {\n    // Filter will be applied automatically due to useEffect dependencies\n    setShowFilterModal(false);\n  };\n\n  const handleNoticeClick = (noticeId) => {\n    navigate(`/notice/${noticeId}`);\n  };\n\n  const handleAdminPanel = () => {\n    navigate('/admin/notice');\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    \n    // Simple B.S. conversion (approximate)\n    const bsYear = year + 57;\n    return `${year}-${month}-${day} (${bsYear}-${month}-${day} B.S)`;\n  };\n\n  const getCategoryDisplayName = (category) => {\n    if (category === 'all') return 'All';\n    return category.split(' ').map(word => \n      word.charAt(0).toUpperCase() + word.slice(1)\n    ).join(' ');\n  };\n\n  // Show all categories\n  const visibleCategories = noticeCategories;\n\n  return (\n    <div className=\"notice-container\">\n      <div className=\"notice-card\">\n        <div className=\"notice-header\">\n          <button className=\"notice-back-btn\" onClick={handleBack}>\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\">\n              <path d=\"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\" fill=\"white\"/>\n            </svg>\n          </button>\n          <h1>Notice</h1>\n          <div className=\"notice-header-actions\">\n            {user && user.role === 'admin' && (\n              <button className=\"admin-btn\" onClick={handleAdminPanel}>\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"white\">\n                  <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n                </svg>\n              </button>\n            )}\n            <button className=\"filter-btn\" onClick={handleFilterClick}>\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"white\">\n                <path d=\"M4.25 5.61C6.27 8.2 10 13 10 13v6c0 .55.45 1 1 1h2c.55 0 1-.45 1-1v-6s3.73-4.8 5.75-7.39C20.25 4.95 19.78 4 18.95 4H5.04c-.83 0-1.3.95-.79 1.61z\"/>\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        <div className=\"notice-search-container\">\n          <div className=\"notice-search-wrapper\">\n            <input\n              type=\"text\"\n              placeholder=\"Search\"\n              value={searchQuery}\n              onChange={handleSearchChange}\n              className=\"notice-search-input\"\n            />\n            <svg className=\"notice-search-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n              <path d=\"M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n          </div>\n        </div>\n\n        <div className=\"notice-province-container\">\n          <select \n            value={selectedProvince} \n            onChange={handleProvinceChange}\n            className=\"notice-province-select\"\n          >\n            {provinces.map(province => (\n              <option key={province} value={province}>\n                {province}\n              </option>\n            ))}\n          </select>\n          <svg className=\"province-dropdown-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n            <path d=\"M7 10l5 5 5-5z\" fill=\"currentColor\"/>\n          </svg>\n        </div>\n\n        <div className=\"notice-categories-container\">\n          <div className=\"notice-categories-scroll\">\n            {visibleCategories.map(category => (\n              <button\n                key={category}\n                className={`category-btn ${selectedCategory === category ? 'active' : ''}`}\n                onClick={() => handleCategorySelect(category)}\n              >\n                {getCategoryDisplayName(category)}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"notice-content\">\n          {loading ? (\n            <div className=\"notice-loading\">\n              <div className=\"loading-spinner\"></div>\n              <p>Loading notices...</p>\n            </div>\n          ) : error ? (\n            <div className=\"notice-error\">\n              <p>{error}</p>\n              <button onClick={fetchNotices} className=\"retry-btn\">Retry</button>\n            </div>\n          ) : notices.length === 0 ? (\n            <div className=\"notice-empty\">\n              <p>No notices found for the selected criteria.</p>\n            </div>\n          ) : (\n            <div className=\"notice-list\">\n              {notices.map((item) => (\n                <div \n                  key={item._id} \n                  className=\"notice-item\"\n                  onClick={() => handleNoticeClick(item._id)}\n                >\n                  <div className=\"notice-item-content\">\n                    <div className=\"notice-item-date\">\n                      {formatDate(item.createdAt)}\n                    </div>\n                    <div className=\"notice-item-title\">\n                      {item.title}\n                    </div>\n                    {item.content && (\n                      <div className=\"notice-item-preview\">\n                        {item.content.substring(0, 100)}...\n                      </div>\n                    )}\n                    <div className=\"notice-item-meta\">\n                      <span className=\"notice-category\">{getCategoryDisplayName(item.category)}</span>\n                      {item.province !== 'All Provinces' && (\n                        <span className=\"notice-province\">{item.province}</span>\n                      )}\n                    </div>\n                  </div>\n                  {item.image && (\n                    <div className=\"notice-item-image\">\n                      <img \n                        src={`http://localhost:5000${item.image.url}`} \n                        alt={item.title}\n                        onError={(e) => {\n                          e.target.style.display = 'none';\n                        }}\n                      />\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n\n      <FilterModal\n        isOpen={showFilterModal}\n        onClose={handleFilterClose}\n        selectedCategory={selectedCategory}\n        selectedProvince={selectedProvince}\n        onCategoryChange={setSelectedCategory}\n        onProvinceChange={setSelectedProvince}\n        onApply={handleFilterApply}\n      />\n    </div>\n  );\n};\n\nexport default Notice;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACe,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlB,QAAQ,CAAC,eAAe,CAAC;EACzE,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAM2B,gBAAgB,GAAG,CACvB,KAAK,EACL,WAAW,EACX,iBAAiB,EACjB,YAAY,EACZ,OAAO,EACP,OAAO,EACP,OAAO,EACP,gBAAgB,EAChB,KAAK,EACL,WAAW,EACX,YAAY,EACZ,sBAAsB,EACtB,cAAc,EACd,oBAAoB,EACpB,WAAW,EACX,WAAW,CACZ;EAED,MAAMC,SAAS,GAAG,CAChB,eAAe,EACf,YAAY,EACZ,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,wBAAwB,CACzB;EAED,MAAMC,YAAY,GAAG3B,WAAW,CAAC,YAAY;IAC3C,IAAI;MACFoB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMQ,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MAEpC,IAAIhB,gBAAgB,KAAK,KAAK,EAAE;QAC9Be,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEjB,gBAAgB,CAAC;MAC7C;MAEA,IAAIE,gBAAgB,KAAK,eAAe,EAAE;QACxCa,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEf,gBAAgB,CAAC;MAC7C;MAEA,IAAIE,WAAW,CAACc,IAAI,CAAC,CAAC,EAAE;QACtBH,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEb,WAAW,CAACc,IAAI,CAAC,CAAC,CAAC;MAC7C;MAEA,MAAMC,QAAQ,GAAG,MAAM7B,KAAK,CAAC8B,GAAG,CAAC,oCAAoCL,MAAM,CAACM,QAAQ,CAAC,CAAC,EAAE,EAAE;QACxFC,eAAe,EAAE;MACnB,CAAC,CAAC;MAEFvB,UAAU,CAACoB,QAAQ,CAACI,IAAI,CAACzB,OAAO,IAAI,EAAE,CAAC;IACzC,CAAC,CAAC,OAAO0B,GAAG,EAAE;MACZf,QAAQ,CAAC,yBAAyB,CAAC;MACnCgB,OAAO,CAACjB,KAAK,CAAC,yBAAyB,EAAEgB,GAAG,CAAC;IAC/C,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACP,gBAAgB,EAAEE,gBAAgB,EAAEE,WAAW,CAAC,CAAC;EAErDlB,SAAS,CAAC,MAAM;IACd4B,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAElB,MAAMY,UAAU,GAAGA,CAAA,KAAM;IACvB9B,QAAQ,CAAC,OAAO,CAAC;EACnB,CAAC;EAED,MAAM+B,oBAAoB,GAAIC,QAAQ,IAAK;IACzC3B,mBAAmB,CAAC2B,QAAQ,CAAC;EAC/B,CAAC;EAED,MAAMC,oBAAoB,GAAIC,CAAC,IAAK;IAClC3B,mBAAmB,CAAC2B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EACrC,CAAC;EAED,MAAMC,kBAAkB,GAAIH,CAAC,IAAK;IAChCzB,cAAc,CAACyB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAChC,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9BvB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMwB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BxB,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMyB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACAzB,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAM0B,iBAAiB,GAAIC,QAAQ,IAAK;IACtC1C,QAAQ,CAAC,WAAW0C,QAAQ,EAAE,CAAC;EACjC,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B3C,QAAQ,CAAC,eAAe,CAAC;EAC3B,CAAC;EAED,MAAM4C,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,IAAI,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;;IAEnD;IACA,MAAMG,MAAM,GAAGR,IAAI,GAAG,EAAE;IACxB,OAAO,GAAGA,IAAI,IAAIE,KAAK,IAAII,GAAG,KAAKE,MAAM,IAAIN,KAAK,IAAII,GAAG,OAAO;EAClE,CAAC;EAED,MAAMG,sBAAsB,GAAIzB,QAAQ,IAAK;IAC3C,IAAIA,QAAQ,KAAK,KAAK,EAAE,OAAO,KAAK;IACpC,OAAOA,QAAQ,CAAC0B,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,IAAI,IACjCA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAC7C,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACb,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGjD,gBAAgB;EAE1C,oBACEnB,OAAA;IAAKqE,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/BtE,OAAA;MAAKqE,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BtE,OAAA;QAAKqE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BtE,OAAA;UAAQqE,SAAS,EAAC,iBAAiB;UAACE,OAAO,EAAEtC,UAAW;UAAAqC,QAAA,eACtDtE,OAAA;YAAKwE,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC7CtE,OAAA;cAAM2E,CAAC,EAAC,8DAA8D;cAACC,IAAI,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACThF,OAAA;UAAAsE,QAAA,EAAI;QAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACfhF,OAAA;UAAKqE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GACnClE,IAAI,IAAIA,IAAI,CAAC6E,IAAI,KAAK,OAAO,iBAC5BjF,OAAA;YAAQqE,SAAS,EAAC,WAAW;YAACE,OAAO,EAAEzB,gBAAiB;YAAAwB,QAAA,eACtDtE,OAAA;cAAKwE,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACE,IAAI,EAAC,OAAO;cAAAN,QAAA,eAC1DtE,OAAA;gBAAM2E,CAAC,EAAC;cAAuH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9H;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CACT,eACDhF,OAAA;YAAQqE,SAAS,EAAC,YAAY;YAACE,OAAO,EAAE9B,iBAAkB;YAAA6B,QAAA,eACxDtE,OAAA;cAAKwE,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACE,IAAI,EAAC,OAAO;cAAAN,QAAA,eAC1DtE,OAAA;gBAAM2E,CAAC,EAAC;cAAkJ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhF,OAAA;QAAKqE,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACtCtE,OAAA;UAAKqE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCtE,OAAA;YACEkF,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,QAAQ;YACpB5C,KAAK,EAAE5B,WAAY;YACnByE,QAAQ,EAAE5C,kBAAmB;YAC7B6B,SAAS,EAAC;UAAqB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACFhF,OAAA;YAAKqE,SAAS,EAAC,oBAAoB;YAACG,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACE,IAAI,EAAC,MAAM;YAAAN,QAAA,eACxFtE,OAAA;cAAM2E,CAAC,EAAC,4IAA4I;cAACU,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhF,OAAA;QAAKqE,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxCtE,OAAA;UACEuC,KAAK,EAAE9B,gBAAiB;UACxB2E,QAAQ,EAAEhD,oBAAqB;UAC/BiC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAEjClD,SAAS,CAAC0C,GAAG,CAAC2B,QAAQ,iBACrBzF,OAAA;YAAuBuC,KAAK,EAAEkD,QAAS;YAAAnB,QAAA,EACpCmB;UAAQ,GADEA,QAAQ;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEb,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACThF,OAAA;UAAKqE,SAAS,EAAC,wBAAwB;UAACG,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACE,IAAI,EAAC,MAAM;UAAAN,QAAA,eAC5FtE,OAAA;YAAM2E,CAAC,EAAC,gBAAgB;YAACC,IAAI,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhF,OAAA;QAAKqE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CtE,OAAA;UAAKqE,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACtCF,iBAAiB,CAACN,GAAG,CAAC3B,QAAQ,iBAC7BnC,OAAA;YAEEqE,SAAS,EAAE,gBAAgB9D,gBAAgB,KAAK4B,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC3EoC,OAAO,EAAEA,CAAA,KAAMrC,oBAAoB,CAACC,QAAQ,CAAE;YAAAmC,QAAA,EAE7CV,sBAAsB,CAACzB,QAAQ;UAAC,GAJ5BA,QAAQ;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKP,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhF,OAAA;QAAKqE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BzD,OAAO,gBACNb,OAAA;UAAKqE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BtE,OAAA;YAAKqE,SAAS,EAAC;UAAiB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvChF,OAAA;YAAAsE,QAAA,EAAG;UAAkB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,GACJjE,KAAK,gBACPf,OAAA;UAAKqE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BtE,OAAA;YAAAsE,QAAA,EAAIvD;UAAK;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACdhF,OAAA;YAAQuE,OAAO,EAAElD,YAAa;YAACgD,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,GACJ3E,OAAO,CAACqF,MAAM,KAAK,CAAC,gBACtB1F,OAAA;UAAKqE,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BtE,OAAA;YAAAsE,QAAA,EAAG;UAA2C;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,gBAENhF,OAAA;UAAKqE,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBjE,OAAO,CAACyD,GAAG,CAAE6B,IAAI,iBAChB3F,OAAA;YAEEqE,SAAS,EAAC,aAAa;YACvBE,OAAO,EAAEA,CAAA,KAAM3B,iBAAiB,CAAC+C,IAAI,CAACC,GAAG,CAAE;YAAAtB,QAAA,gBAE3CtE,OAAA;cAAKqE,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClCtE,OAAA;gBAAKqE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAC9BvB,UAAU,CAAC4C,IAAI,CAACE,SAAS;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACNhF,OAAA;gBAAKqE,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAC/BqB,IAAI,CAACG;cAAK;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,EACLW,IAAI,CAACI,OAAO,iBACX/F,OAAA;gBAAKqE,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,GACjCqB,IAAI,CAACI,OAAO,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KAClC;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,eACDhF,OAAA;gBAAKqE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BtE,OAAA;kBAAMqE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAEV,sBAAsB,CAAC+B,IAAI,CAACxD,QAAQ;gBAAC;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC/EW,IAAI,CAACF,QAAQ,KAAK,eAAe,iBAChCzF,OAAA;kBAAMqE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAEqB,IAAI,CAACF;gBAAQ;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACxD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLW,IAAI,CAACM,KAAK,iBACTjG,OAAA;cAAKqE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCtE,OAAA;gBACEkG,GAAG,EAAE,wBAAwBP,IAAI,CAACM,KAAK,CAACE,GAAG,EAAG;gBAC9CC,GAAG,EAAET,IAAI,CAACG,KAAM;gBAChBO,OAAO,EAAGhE,CAAC,IAAK;kBACdA,CAAC,CAACC,MAAM,CAACgE,KAAK,CAACC,OAAO,GAAG,MAAM;gBACjC;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA,GAjCIW,IAAI,CAACC,GAAG;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkCV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhF,OAAA,CAACF,WAAW;MACV0G,MAAM,EAAEvF,eAAgB;MACxBwF,OAAO,EAAE/D,iBAAkB;MAC3BnC,gBAAgB,EAAEA,gBAAiB;MACnCE,gBAAgB,EAAEA,gBAAiB;MACnCiG,gBAAgB,EAAElG,mBAAoB;MACtCmG,gBAAgB,EAAEjG,mBAAoB;MACtCkG,OAAO,EAAEjE;IAAkB;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC9E,EAAA,CApRID,MAAM;EAAA,QACON,WAAW,EACXC,OAAO;AAAA;AAAAiH,EAAA,GAFpB5G,MAAM;AAsRZ,eAAeA,MAAM;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}