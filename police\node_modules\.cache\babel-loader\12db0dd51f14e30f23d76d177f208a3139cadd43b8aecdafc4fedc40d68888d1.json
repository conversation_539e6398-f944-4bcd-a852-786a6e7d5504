{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\POlice\\\\police\\\\src\\\\component\\\\AdminNotice.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport axios from 'axios';\nimport './AdminNotice.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminNotice = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useAuth();\n  const [notices, setNotices] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [showForm, setShowForm] = useState(false);\n  const [editingNotice, setEditingNotice] = useState(null);\n  const [formData, setFormData] = useState({\n    title: '',\n    content: '',\n    category: 'promotion',\n    province: 'All Provinces',\n    priority: 'medium'\n  });\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState('');\n  const noticeCategories = ['promotion', 'transfer notice', 'directives', 'rules', 'exams', 'order', 'general notice', 'law', 'un notice', 'deputation', 'other notice(career)', 'bipad notice', 'public procurement', 'ordinance', 'procedure'];\n  const provinces = ['All Provinces', 'Province 1', 'Madhesh Province', 'Bagmati Province', 'Gandaki Province', 'Lumbini Province', 'Karnali Province', 'Sudurpashchim Province'];\n  useEffect(() => {\n    if (!user || user.role !== 'admin') {\n      navigate('/login');\n      return;\n    }\n    fetchNotices();\n  }, [user, navigate]);\n  const fetchNotices = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/notice', {\n        withCredentials: true\n      });\n      setNotices(response.data.notices || []);\n    } catch (err) {\n      setError('Failed to fetch notices');\n      console.error('Error fetching notices:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleBack = () => {\n    navigate('/notice');\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleImageChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setImagePreview(reader.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      const submitData = new FormData();\n      submitData.append('title', formData.title);\n      submitData.append('content', formData.content);\n      submitData.append('category', formData.category);\n      submitData.append('province', formData.province);\n      submitData.append('priority', formData.priority);\n      if (selectedImage) {\n        submitData.append('image', selectedImage);\n      }\n      if (editingNotice) {\n        await axios.put(`http://localhost:5000/api/notice/${editingNotice._id}`, submitData, {\n          withCredentials: true,\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n      } else {\n        await axios.post('http://localhost:5000/api/notice', submitData, {\n          withCredentials: true,\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n      }\n\n      // Reset form\n      setFormData({\n        title: '',\n        content: '',\n        category: 'promotion',\n        province: 'All Provinces',\n        priority: 'medium'\n      });\n      setSelectedImage(null);\n      setImagePreview('');\n      setShowForm(false);\n      setEditingNotice(null);\n\n      // Refresh notices\n      fetchNotices();\n    } catch (err) {\n      console.error('Error saving notice:', err);\n      setError('Failed to save notice');\n    }\n  };\n  const handleEdit = notice => {\n    setEditingNotice(notice);\n    setFormData({\n      title: notice.title,\n      content: notice.content,\n      category: notice.category,\n      province: notice.province,\n      priority: notice.priority\n    });\n    if (notice.image) {\n      setImagePreview(`http://localhost:5000${notice.image.url}`);\n    }\n    setShowForm(true);\n  };\n  const handleDelete = async noticeId => {\n    if (window.confirm('Are you sure you want to delete this notice?')) {\n      try {\n        await axios.delete(`http://localhost:5000/api/notice/${noticeId}`, {\n          withCredentials: true\n        });\n        fetchNotices();\n      } catch (err) {\n        console.error('Error deleting notice:', err);\n        setError('Failed to delete notice');\n      }\n    }\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n\n    // Simple B.S. conversion (approximate)\n    const bsYear = year + 57;\n    return `${year}-${month}-${day} (${bsYear}-${month}-${day} B.S)`;\n  };\n  const getCategoryDisplayName = category => {\n    return category.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');\n  };\n  if (!user || user.role !== 'admin') {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-notice-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-notice-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-notice-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"admin-notice-back-btn\",\n          onClick: handleBack,\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"24\",\n            height: \"24\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\",\n              fill: \"white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Admin - Notice Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"add-notice-btn\",\n          onClick: () => {\n            setShowForm(true);\n            setEditingNotice(null);\n            setFormData({\n              title: '',\n              content: '',\n              category: 'promotion',\n              province: 'All Provinces',\n              priority: 'medium'\n            });\n            setSelectedImage(null);\n            setImagePreview('');\n          },\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"white\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), showForm && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notice-form-container\",\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"notice-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"title\",\n              children: \"Title *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"title\",\n              name: \"title\",\n              value: formData.title,\n              onChange: handleInputChange,\n              required: true,\n              placeholder: \"Enter notice title\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"content\",\n              children: \"Content *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              id: \"content\",\n              name: \"content\",\n              value: formData.content,\n              onChange: handleInputChange,\n              required: true,\n              rows: \"6\",\n              placeholder: \"Enter notice content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"category\",\n                children: \"Category *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"category\",\n                name: \"category\",\n                value: formData.category,\n                onChange: handleInputChange,\n                required: true,\n                children: noticeCategories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category,\n                  children: getCategoryDisplayName(category)\n                }, category, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"province\",\n                children: \"Province\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"province\",\n                name: \"province\",\n                value: formData.province,\n                onChange: handleInputChange,\n                children: provinces.map(province => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: province,\n                  children: province\n                }, province, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"priority\",\n              children: \"Priority\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"priority\",\n              name: \"priority\",\n              value: formData.priority,\n              onChange: handleInputChange,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"low\",\n                children: \"Low\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"medium\",\n                children: \"Medium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"high\",\n                children: \"High\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"image\",\n              children: \"Image (Optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              id: \"image\",\n              accept: \"image/*\",\n              onChange: handleImageChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this), imagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"image-preview\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: imagePreview,\n                alt: \"Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowForm(false),\n              className: \"cancel-btn\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"submit-btn\",\n              children: editingNotice ? 'Update Notice' : 'Create Notice'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-notice-content\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"admin-notice-loading\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading notices...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"admin-notice-error\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: fetchNotices,\n            className: \"retry-btn\",\n            children: \"Retry\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 13\n        }, this) : notices.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"admin-notice-empty\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No notices found. Create your first notice!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"admin-notice-list\",\n          children: notices.map(notice => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"admin-notice-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"admin-notice-item-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"admin-notice-item-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"admin-notice-item-date\",\n                  children: formatDate(notice.createdAt)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"admin-notice-item-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(notice),\n                    className: \"edit-btn\",\n                    title: \"Edit\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"16\",\n                      height: \"16\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 371,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 370,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(notice._id),\n                    className: \"delete-btn\",\n                    title: \"Delete\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"16\",\n                      height: \"16\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 380,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 379,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"admin-notice-item-title\",\n                children: notice.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"admin-notice-item-preview\",\n                children: [notice.content.substring(0, 150), \"...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"admin-notice-item-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"notice-category\",\n                  children: getCategoryDisplayName(notice.category)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 23\n                }, this), notice.province !== 'All Provinces' && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"notice-province\",\n                  children: notice.province\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"notice-priority priority-{notice.priority}\",\n                  children: notice.priority\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"admin-notice-item-stats\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Views: \", notice.views || 0]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Likes: \", notice.likes || 0]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 19\n            }, this), notice.image && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"admin-notice-item-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: `http://localhost:5000${notice.image.url}`,\n                alt: notice.title,\n                onError: e => {\n                  e.target.style.display = 'none';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 21\n            }, this)]\n          }, notice._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 204,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminNotice, \"+EmZHxb9Y4lUgvvV33pJMSeFyd4=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = AdminNotice;\nexport default AdminNotice;\nvar _c;\n$RefreshReg$(_c, \"AdminNotice\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useAuth", "axios", "jsxDEV", "_jsxDEV", "AdminNotice", "_s", "navigate", "user", "notices", "setNotices", "loading", "setLoading", "error", "setError", "showForm", "setShowForm", "editingNotice", "setEditingNotice", "formData", "setFormData", "title", "content", "category", "province", "priority", "selectedImage", "setSelectedImage", "imagePreview", "setImagePreview", "noticeCategories", "provinces", "role", "fetchNotices", "response", "get", "withCredentials", "data", "err", "console", "handleBack", "handleInputChange", "e", "name", "value", "target", "prev", "handleImageChange", "file", "files", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleSubmit", "preventDefault", "submitData", "FormData", "append", "put", "_id", "headers", "post", "handleEdit", "notice", "image", "url", "handleDelete", "noticeId", "window", "confirm", "delete", "formatDate", "dateString", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "bsYear", "getCategoryDisplayName", "split", "map", "word", "char<PERSON>t", "toUpperCase", "slice", "join", "className", "children", "onClick", "width", "height", "viewBox", "d", "fill", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "placeholder", "rows", "accept", "src", "alt", "length", "createdAt", "substring", "views", "likes", "onError", "style", "display", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/POlice/police/src/component/AdminNotice.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport axios from 'axios';\nimport './AdminNotice.css';\n\nconst AdminNotice = () => {\n  const navigate = useNavigate();\n  const { user } = useAuth();\n  const [notices, setNotices] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [showForm, setShowForm] = useState(false);\n  const [editingNotice, setEditingNotice] = useState(null);\n  const [formData, setFormData] = useState({\n    title: '',\n    content: '',\n    category: 'promotion',\n    province: 'All Provinces',\n    priority: 'medium'\n  });\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState('');\n\n  const noticeCategories = [\n    'promotion',\n    'transfer notice',\n    'directives',\n    'rules',\n    'exams',\n    'order',\n    'general notice',\n    'law',\n    'un notice',\n    'deputation',\n    'other notice(career)',\n    'bipad notice',\n    'public procurement',\n    'ordinance',\n    'procedure'\n  ];\n\n  const provinces = [\n    'All Provinces',\n    'Province 1',\n    'Madhesh Province',\n    'Bagmati Province',\n    'Gandaki Province',\n    'Lumbini Province',\n    'Karnali Province',\n    'Sudurpashchim Province'\n  ];\n\n  useEffect(() => {\n    if (!user || user.role !== 'admin') {\n      navigate('/login');\n      return;\n    }\n    fetchNotices();\n  }, [user, navigate]);\n\n  const fetchNotices = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/notice', {\n        withCredentials: true\n      });\n      setNotices(response.data.notices || []);\n    } catch (err) {\n      setError('Failed to fetch notices');\n      console.error('Error fetching notices:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleBack = () => {\n    navigate('/notice');\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleImageChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setImagePreview(reader.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    try {\n      const submitData = new FormData();\n      submitData.append('title', formData.title);\n      submitData.append('content', formData.content);\n      submitData.append('category', formData.category);\n      submitData.append('province', formData.province);\n      submitData.append('priority', formData.priority);\n      \n      if (selectedImage) {\n        submitData.append('image', selectedImage);\n      }\n\n      if (editingNotice) {\n        await axios.put(`http://localhost:5000/api/notice/${editingNotice._id}`, submitData, {\n          withCredentials: true,\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n      } else {\n        await axios.post('http://localhost:5000/api/notice', submitData, {\n          withCredentials: true,\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n      }\n\n      // Reset form\n      setFormData({\n        title: '',\n        content: '',\n        category: 'promotion',\n        province: 'All Provinces',\n        priority: 'medium'\n      });\n      setSelectedImage(null);\n      setImagePreview('');\n      setShowForm(false);\n      setEditingNotice(null);\n      \n      // Refresh notices\n      fetchNotices();\n    } catch (err) {\n      console.error('Error saving notice:', err);\n      setError('Failed to save notice');\n    }\n  };\n\n  const handleEdit = (notice) => {\n    setEditingNotice(notice);\n    setFormData({\n      title: notice.title,\n      content: notice.content,\n      category: notice.category,\n      province: notice.province,\n      priority: notice.priority\n    });\n    if (notice.image) {\n      setImagePreview(`http://localhost:5000${notice.image.url}`);\n    }\n    setShowForm(true);\n  };\n\n  const handleDelete = async (noticeId) => {\n    if (window.confirm('Are you sure you want to delete this notice?')) {\n      try {\n        await axios.delete(`http://localhost:5000/api/notice/${noticeId}`, {\n          withCredentials: true\n        });\n        fetchNotices();\n      } catch (err) {\n        console.error('Error deleting notice:', err);\n        setError('Failed to delete notice');\n      }\n    }\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    \n    // Simple B.S. conversion (approximate)\n    const bsYear = year + 57;\n    return `${year}-${month}-${day} (${bsYear}-${month}-${day} B.S)`;\n  };\n\n  const getCategoryDisplayName = (category) => {\n    return category.split(' ').map(word => \n      word.charAt(0).toUpperCase() + word.slice(1)\n    ).join(' ');\n  };\n\n  if (!user || user.role !== 'admin') {\n    return null;\n  }\n\n  return (\n    <div className=\"admin-notice-container\">\n      <div className=\"admin-notice-card\">\n        <div className=\"admin-notice-header\">\n          <button className=\"admin-notice-back-btn\" onClick={handleBack}>\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\">\n              <path d=\"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\" fill=\"white\"/>\n            </svg>\n          </button>\n          <h1>Admin - Notice Management</h1>\n          <button \n            className=\"add-notice-btn\"\n            onClick={() => {\n              setShowForm(true);\n              setEditingNotice(null);\n              setFormData({\n                title: '',\n                content: '',\n                category: 'promotion',\n                province: 'All Provinces',\n                priority: 'medium'\n              });\n              setSelectedImage(null);\n              setImagePreview('');\n            }}\n          >\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"white\">\n              <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z\"/>\n            </svg>\n          </button>\n        </div>\n\n        {showForm && (\n          <div className=\"notice-form-container\">\n            <form onSubmit={handleSubmit} className=\"notice-form\">\n              <div className=\"form-group\">\n                <label htmlFor=\"title\">Title *</label>\n                <input\n                  type=\"text\"\n                  id=\"title\"\n                  name=\"title\"\n                  value={formData.title}\n                  onChange={handleInputChange}\n                  required\n                  placeholder=\"Enter notice title\"\n                />\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"content\">Content *</label>\n                <textarea\n                  id=\"content\"\n                  name=\"content\"\n                  value={formData.content}\n                  onChange={handleInputChange}\n                  required\n                  rows=\"6\"\n                  placeholder=\"Enter notice content\"\n                />\n              </div>\n\n              <div className=\"form-row\">\n                <div className=\"form-group\">\n                  <label htmlFor=\"category\">Category *</label>\n                  <select\n                    id=\"category\"\n                    name=\"category\"\n                    value={formData.category}\n                    onChange={handleInputChange}\n                    required\n                  >\n                    {noticeCategories.map(category => (\n                      <option key={category} value={category}>\n                        {getCategoryDisplayName(category)}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"province\">Province</label>\n                  <select\n                    id=\"province\"\n                    name=\"province\"\n                    value={formData.province}\n                    onChange={handleInputChange}\n                  >\n                    {provinces.map(province => (\n                      <option key={province} value={province}>\n                        {province}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"priority\">Priority</label>\n                <select\n                  id=\"priority\"\n                  name=\"priority\"\n                  value={formData.priority}\n                  onChange={handleInputChange}\n                >\n                  <option value=\"low\">Low</option>\n                  <option value=\"medium\">Medium</option>\n                  <option value=\"high\">High</option>\n                </select>\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"image\">Image (Optional)</label>\n                <input\n                  type=\"file\"\n                  id=\"image\"\n                  accept=\"image/*\"\n                  onChange={handleImageChange}\n                />\n                {imagePreview && (\n                  <div className=\"image-preview\">\n                    <img src={imagePreview} alt=\"Preview\" />\n                  </div>\n                )}\n              </div>\n\n              <div className=\"form-actions\">\n                <button type=\"button\" onClick={() => setShowForm(false)} className=\"cancel-btn\">\n                  Cancel\n                </button>\n                <button type=\"submit\" className=\"submit-btn\">\n                  {editingNotice ? 'Update Notice' : 'Create Notice'}\n                </button>\n              </div>\n            </form>\n          </div>\n        )}\n\n        <div className=\"admin-notice-content\">\n          {loading ? (\n            <div className=\"admin-notice-loading\">\n              <div className=\"loading-spinner\"></div>\n              <p>Loading notices...</p>\n            </div>\n          ) : error ? (\n            <div className=\"admin-notice-error\">\n              <p>{error}</p>\n              <button onClick={fetchNotices} className=\"retry-btn\">Retry</button>\n            </div>\n          ) : notices.length === 0 ? (\n            <div className=\"admin-notice-empty\">\n              <p>No notices found. Create your first notice!</p>\n            </div>\n          ) : (\n            <div className=\"admin-notice-list\">\n              {notices.map((notice) => (\n                <div key={notice._id} className=\"admin-notice-item\">\n                  <div className=\"admin-notice-item-content\">\n                    <div className=\"admin-notice-item-header\">\n                      <div className=\"admin-notice-item-date\">\n                        {formatDate(notice.createdAt)}\n                      </div>\n                      <div className=\"admin-notice-item-actions\">\n                        <button \n                          onClick={() => handleEdit(notice)}\n                          className=\"edit-btn\"\n                          title=\"Edit\"\n                        >\n                          <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                            <path d=\"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"/>\n                          </svg>\n                        </button>\n                        <button \n                          onClick={() => handleDelete(notice._id)}\n                          className=\"delete-btn\"\n                          title=\"Delete\"\n                        >\n                          <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                            <path d=\"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z\"/>\n                          </svg>\n                        </button>\n                      </div>\n                    </div>\n                    <div className=\"admin-notice-item-title\">\n                      {notice.title}\n                    </div>\n                    <div className=\"admin-notice-item-preview\">\n                      {notice.content.substring(0, 150)}...\n                    </div>\n                    <div className=\"admin-notice-item-meta\">\n                      <span className=\"notice-category\">{getCategoryDisplayName(notice.category)}</span>\n                      {notice.province !== 'All Provinces' && (\n                        <span className=\"notice-province\">{notice.province}</span>\n                      )}\n                      <span className=\"notice-priority priority-{notice.priority}\">{notice.priority}</span>\n                    </div>\n                    <div className=\"admin-notice-item-stats\">\n                      <span>Views: {notice.views || 0}</span>\n                      <span>Likes: {notice.likes || 0}</span>\n                    </div>\n                  </div>\n                  {notice.image && (\n                    <div className=\"admin-notice-item-image\">\n                      <img \n                        src={`http://localhost:5000${notice.image.url}`} \n                        alt={notice.title}\n                        onError={(e) => {\n                          e.target.style.display = 'none';\n                        }}\n                      />\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminNotice;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEQ;EAAK,CAAC,GAAGP,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC;IACvCuB,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,WAAW;IACrBC,QAAQ,EAAE,eAAe;IACzBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAMgC,gBAAgB,GAAG,CACvB,WAAW,EACX,iBAAiB,EACjB,YAAY,EACZ,OAAO,EACP,OAAO,EACP,OAAO,EACP,gBAAgB,EAChB,KAAK,EACL,WAAW,EACX,YAAY,EACZ,sBAAsB,EACtB,cAAc,EACd,oBAAoB,EACpB,WAAW,EACX,WAAW,CACZ;EAED,MAAMC,SAAS,GAAG,CAChB,eAAe,EACf,YAAY,EACZ,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,wBAAwB,CACzB;EAEDhC,SAAS,CAAC,MAAM;IACd,IAAI,CAACS,IAAI,IAAIA,IAAI,CAACwB,IAAI,KAAK,OAAO,EAAE;MAClCzB,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IACA0B,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACzB,IAAI,EAAED,QAAQ,CAAC,CAAC;EAEpB,MAAM0B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFrB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMsB,QAAQ,GAAG,MAAMhC,KAAK,CAACiC,GAAG,CAAC,kCAAkC,EAAE;QACnEC,eAAe,EAAE;MACnB,CAAC,CAAC;MACF1B,UAAU,CAACwB,QAAQ,CAACG,IAAI,CAAC5B,OAAO,IAAI,EAAE,CAAC;IACzC,CAAC,CAAC,OAAO6B,GAAG,EAAE;MACZxB,QAAQ,CAAC,yBAAyB,CAAC;MACnCyB,OAAO,CAAC1B,KAAK,CAAC,yBAAyB,EAAEyB,GAAG,CAAC;IAC/C,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4B,UAAU,GAAGA,CAAA,KAAM;IACvBjC,QAAQ,CAAC,SAAS,CAAC;EACrB,CAAC;EAED,MAAMkC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCzB,WAAW,CAAC0B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,iBAAiB,GAAIL,CAAC,IAAK;IAC/B,MAAMM,IAAI,GAAGN,CAAC,CAACG,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACRrB,gBAAgB,CAACqB,IAAI,CAAC;MACtB,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACvBvB,eAAe,CAACqB,MAAM,CAACG,MAAM,CAAC;MAChC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACN,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMO,YAAY,GAAG,MAAOb,CAAC,IAAK;IAChCA,CAAC,CAACc,cAAc,CAAC,CAAC;IAElB,IAAI;MACF,MAAMC,UAAU,GAAG,IAAIC,QAAQ,CAAC,CAAC;MACjCD,UAAU,CAACE,MAAM,CAAC,OAAO,EAAExC,QAAQ,CAACE,KAAK,CAAC;MAC1CoC,UAAU,CAACE,MAAM,CAAC,SAAS,EAAExC,QAAQ,CAACG,OAAO,CAAC;MAC9CmC,UAAU,CAACE,MAAM,CAAC,UAAU,EAAExC,QAAQ,CAACI,QAAQ,CAAC;MAChDkC,UAAU,CAACE,MAAM,CAAC,UAAU,EAAExC,QAAQ,CAACK,QAAQ,CAAC;MAChDiC,UAAU,CAACE,MAAM,CAAC,UAAU,EAAExC,QAAQ,CAACM,QAAQ,CAAC;MAEhD,IAAIC,aAAa,EAAE;QACjB+B,UAAU,CAACE,MAAM,CAAC,OAAO,EAAEjC,aAAa,CAAC;MAC3C;MAEA,IAAIT,aAAa,EAAE;QACjB,MAAMf,KAAK,CAAC0D,GAAG,CAAC,oCAAoC3C,aAAa,CAAC4C,GAAG,EAAE,EAAEJ,UAAU,EAAE;UACnFrB,eAAe,EAAE,IAAI;UACrB0B,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM5D,KAAK,CAAC6D,IAAI,CAAC,kCAAkC,EAAEN,UAAU,EAAE;UAC/DrB,eAAe,EAAE,IAAI;UACrB0B,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;MACJ;;MAEA;MACA1C,WAAW,CAAC;QACVC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACXC,QAAQ,EAAE,WAAW;QACrBC,QAAQ,EAAE,eAAe;QACzBC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACFE,gBAAgB,CAAC,IAAI,CAAC;MACtBE,eAAe,CAAC,EAAE,CAAC;MACnBb,WAAW,CAAC,KAAK,CAAC;MAClBE,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACAe,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOK,GAAG,EAAE;MACZC,OAAO,CAAC1B,KAAK,CAAC,sBAAsB,EAAEyB,GAAG,CAAC;MAC1CxB,QAAQ,CAAC,uBAAuB,CAAC;IACnC;EACF,CAAC;EAED,MAAMkD,UAAU,GAAIC,MAAM,IAAK;IAC7B/C,gBAAgB,CAAC+C,MAAM,CAAC;IACxB7C,WAAW,CAAC;MACVC,KAAK,EAAE4C,MAAM,CAAC5C,KAAK;MACnBC,OAAO,EAAE2C,MAAM,CAAC3C,OAAO;MACvBC,QAAQ,EAAE0C,MAAM,CAAC1C,QAAQ;MACzBC,QAAQ,EAAEyC,MAAM,CAACzC,QAAQ;MACzBC,QAAQ,EAAEwC,MAAM,CAACxC;IACnB,CAAC,CAAC;IACF,IAAIwC,MAAM,CAACC,KAAK,EAAE;MAChBrC,eAAe,CAAC,wBAAwBoC,MAAM,CAACC,KAAK,CAACC,GAAG,EAAE,CAAC;IAC7D;IACAnD,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMoD,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvC,IAAIC,MAAM,CAACC,OAAO,CAAC,8CAA8C,CAAC,EAAE;MAClE,IAAI;QACF,MAAMrE,KAAK,CAACsE,MAAM,CAAC,oCAAoCH,QAAQ,EAAE,EAAE;UACjEjC,eAAe,EAAE;QACnB,CAAC,CAAC;QACFH,YAAY,CAAC,CAAC;MAChB,CAAC,CAAC,OAAOK,GAAG,EAAE;QACZC,OAAO,CAAC1B,KAAK,CAAC,wBAAwB,EAAEyB,GAAG,CAAC;QAC5CxB,QAAQ,CAAC,yBAAyB,CAAC;MACrC;IACF;EACF,CAAC;EAED,MAAM2D,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,IAAI,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;;IAEnD;IACA,MAAMG,MAAM,GAAGR,IAAI,GAAG,EAAE;IACxB,OAAO,GAAGA,IAAI,IAAIE,KAAK,IAAII,GAAG,KAAKE,MAAM,IAAIN,KAAK,IAAII,GAAG,OAAO;EAClE,CAAC;EAED,MAAMG,sBAAsB,GAAI/D,QAAQ,IAAK;IAC3C,OAAOA,QAAQ,CAACgE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,IAAI,IACjCA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAC7C,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACb,CAAC;EAED,IAAI,CAACrF,IAAI,IAAIA,IAAI,CAACwB,IAAI,KAAK,OAAO,EAAE;IAClC,OAAO,IAAI;EACb;EAEA,oBACE5B,OAAA;IAAK0F,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACrC3F,OAAA;MAAK0F,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC3F,OAAA;QAAK0F,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClC3F,OAAA;UAAQ0F,SAAS,EAAC,uBAAuB;UAACE,OAAO,EAAExD,UAAW;UAAAuD,QAAA,eAC5D3F,OAAA;YAAK6F,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC7C3F,OAAA;cAAMgG,CAAC,EAAC,8DAA8D;cAACC,IAAI,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACTrG,OAAA;UAAA2F,QAAA,EAAI;QAAyB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClCrG,OAAA;UACE0F,SAAS,EAAC,gBAAgB;UAC1BE,OAAO,EAAEA,CAAA,KAAM;YACbhF,WAAW,CAAC,IAAI,CAAC;YACjBE,gBAAgB,CAAC,IAAI,CAAC;YACtBE,WAAW,CAAC;cACVC,KAAK,EAAE,EAAE;cACTC,OAAO,EAAE,EAAE;cACXC,QAAQ,EAAE,WAAW;cACrBC,QAAQ,EAAE,eAAe;cACzBC,QAAQ,EAAE;YACZ,CAAC,CAAC;YACFE,gBAAgB,CAAC,IAAI,CAAC;YACtBE,eAAe,CAAC,EAAE,CAAC;UACrB,CAAE;UAAAkE,QAAA,eAEF3F,OAAA;YAAK6F,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACE,IAAI,EAAC,OAAO;YAAAN,QAAA,eAC1D3F,OAAA;cAAMgG,CAAC,EAAC;YAAsG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7G;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL1F,QAAQ,iBACPX,OAAA;QAAK0F,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpC3F,OAAA;UAAMsG,QAAQ,EAAEnD,YAAa;UAACuC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACnD3F,OAAA;YAAK0F,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3F,OAAA;cAAOuG,OAAO,EAAC,OAAO;cAAAZ,QAAA,EAAC;YAAO;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCrG,OAAA;cACEwG,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,OAAO;cACVlE,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEzB,QAAQ,CAACE,KAAM;cACtByF,QAAQ,EAAErE,iBAAkB;cAC5BsE,QAAQ;cACRC,WAAW,EAAC;YAAoB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENrG,OAAA;YAAK0F,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3F,OAAA;cAAOuG,OAAO,EAAC,SAAS;cAAAZ,QAAA,EAAC;YAAS;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CrG,OAAA;cACEyG,EAAE,EAAC,SAAS;cACZlE,IAAI,EAAC,SAAS;cACdC,KAAK,EAAEzB,QAAQ,CAACG,OAAQ;cACxBwF,QAAQ,EAAErE,iBAAkB;cAC5BsE,QAAQ;cACRE,IAAI,EAAC,GAAG;cACRD,WAAW,EAAC;YAAsB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENrG,OAAA;YAAK0F,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB3F,OAAA;cAAK0F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB3F,OAAA;gBAAOuG,OAAO,EAAC,UAAU;gBAAAZ,QAAA,EAAC;cAAU;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5CrG,OAAA;gBACEyG,EAAE,EAAC,UAAU;gBACblE,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAEzB,QAAQ,CAACI,QAAS;gBACzBuF,QAAQ,EAAErE,iBAAkB;gBAC5BsE,QAAQ;gBAAAhB,QAAA,EAEPjE,gBAAgB,CAAC0D,GAAG,CAACjE,QAAQ,iBAC5BnB,OAAA;kBAAuBwC,KAAK,EAAErB,QAAS;kBAAAwE,QAAA,EACpCT,sBAAsB,CAAC/D,QAAQ;gBAAC,GADtBA,QAAQ;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENrG,OAAA;cAAK0F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB3F,OAAA;gBAAOuG,OAAO,EAAC,UAAU;gBAAAZ,QAAA,EAAC;cAAQ;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1CrG,OAAA;gBACEyG,EAAE,EAAC,UAAU;gBACblE,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAEzB,QAAQ,CAACK,QAAS;gBACzBsF,QAAQ,EAAErE,iBAAkB;gBAAAsD,QAAA,EAE3BhE,SAAS,CAACyD,GAAG,CAAChE,QAAQ,iBACrBpB,OAAA;kBAAuBwC,KAAK,EAAEpB,QAAS;kBAAAuE,QAAA,EACpCvE;gBAAQ,GADEA,QAAQ;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrG,OAAA;YAAK0F,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3F,OAAA;cAAOuG,OAAO,EAAC,UAAU;cAAAZ,QAAA,EAAC;YAAQ;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CrG,OAAA;cACEyG,EAAE,EAAC,UAAU;cACblE,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEzB,QAAQ,CAACM,QAAS;cACzBqF,QAAQ,EAAErE,iBAAkB;cAAAsD,QAAA,gBAE5B3F,OAAA;gBAAQwC,KAAK,EAAC,KAAK;gBAAAmD,QAAA,EAAC;cAAG;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChCrG,OAAA;gBAAQwC,KAAK,EAAC,QAAQ;gBAAAmD,QAAA,EAAC;cAAM;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCrG,OAAA;gBAAQwC,KAAK,EAAC,MAAM;gBAAAmD,QAAA,EAAC;cAAI;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENrG,OAAA;YAAK0F,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3F,OAAA;cAAOuG,OAAO,EAAC,OAAO;cAAAZ,QAAA,EAAC;YAAgB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/CrG,OAAA;cACEwG,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,OAAO;cACVK,MAAM,EAAC,SAAS;cAChBJ,QAAQ,EAAE/D;YAAkB;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,EACD7E,YAAY,iBACXxB,OAAA;cAAK0F,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B3F,OAAA;gBAAK+G,GAAG,EAAEvF,YAAa;gBAACwF,GAAG,EAAC;cAAS;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENrG,OAAA;YAAK0F,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B3F,OAAA;cAAQwG,IAAI,EAAC,QAAQ;cAACZ,OAAO,EAAEA,CAAA,KAAMhF,WAAW,CAAC,KAAK,CAAE;cAAC8E,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEhF;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrG,OAAA;cAAQwG,IAAI,EAAC,QAAQ;cAACd,SAAS,EAAC,YAAY;cAAAC,QAAA,EACzC9E,aAAa,GAAG,eAAe,GAAG;YAAe;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,eAEDrG,OAAA;QAAK0F,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAClCpF,OAAO,gBACNP,OAAA;UAAK0F,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC3F,OAAA;YAAK0F,SAAS,EAAC;UAAiB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvCrG,OAAA;YAAA2F,QAAA,EAAG;UAAkB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,GACJ5F,KAAK,gBACPT,OAAA;UAAK0F,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjC3F,OAAA;YAAA2F,QAAA,EAAIlF;UAAK;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACdrG,OAAA;YAAQ4F,OAAO,EAAE/D,YAAa;YAAC6D,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,GACJhG,OAAO,CAAC4G,MAAM,KAAK,CAAC,gBACtBjH,OAAA;UAAK0F,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjC3F,OAAA;YAAA2F,QAAA,EAAG;UAA2C;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,gBAENrG,OAAA;UAAK0F,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC/BtF,OAAO,CAAC+E,GAAG,CAAEvB,MAAM,iBAClB7D,OAAA;YAAsB0F,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjD3F,OAAA;cAAK0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxC3F,OAAA;gBAAK0F,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACvC3F,OAAA;kBAAK0F,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EACpCtB,UAAU,CAACR,MAAM,CAACqD,SAAS;gBAAC;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACNrG,OAAA;kBAAK0F,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACxC3F,OAAA;oBACE4F,OAAO,EAAEA,CAAA,KAAMhC,UAAU,CAACC,MAAM,CAAE;oBAClC6B,SAAS,EAAC,UAAU;oBACpBzE,KAAK,EAAC,MAAM;oBAAA0E,QAAA,eAEZ3F,OAAA;sBAAK6F,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAACC,OAAO,EAAC,WAAW;sBAACE,IAAI,EAAC,cAAc;sBAAAN,QAAA,eACjE3F,OAAA;wBAAMgG,CAAC,EAAC;sBAAuJ;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9J;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACTrG,OAAA;oBACE4F,OAAO,EAAEA,CAAA,KAAM5B,YAAY,CAACH,MAAM,CAACJ,GAAG,CAAE;oBACxCiC,SAAS,EAAC,YAAY;oBACtBzE,KAAK,EAAC,QAAQ;oBAAA0E,QAAA,eAEd3F,OAAA;sBAAK6F,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAACC,OAAO,EAAC,WAAW;sBAACE,IAAI,EAAC,cAAc;sBAAAN,QAAA,eACjE3F,OAAA;wBAAMgG,CAAC,EAAC;sBAA+E;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrG,OAAA;gBAAK0F,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EACrC9B,MAAM,CAAC5C;cAAK;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNrG,OAAA;gBAAK0F,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,GACvC9B,MAAM,CAAC3C,OAAO,CAACiG,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KACpC;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNrG,OAAA;gBAAK0F,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrC3F,OAAA;kBAAM0F,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAET,sBAAsB,CAACrB,MAAM,CAAC1C,QAAQ;gBAAC;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACjFxC,MAAM,CAACzC,QAAQ,KAAK,eAAe,iBAClCpB,OAAA;kBAAM0F,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAE9B,MAAM,CAACzC;gBAAQ;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAC1D,eACDrG,OAAA;kBAAM0F,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAE9B,MAAM,CAACxC;gBAAQ;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC,eACNrG,OAAA;gBAAK0F,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC3F,OAAA;kBAAA2F,QAAA,GAAM,SAAO,EAAC9B,MAAM,CAACuD,KAAK,IAAI,CAAC;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvCrG,OAAA;kBAAA2F,QAAA,GAAM,SAAO,EAAC9B,MAAM,CAACwD,KAAK,IAAI,CAAC;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLxC,MAAM,CAACC,KAAK,iBACX9D,OAAA;cAAK0F,SAAS,EAAC,yBAAyB;cAAAC,QAAA,eACtC3F,OAAA;gBACE+G,GAAG,EAAE,wBAAwBlD,MAAM,CAACC,KAAK,CAACC,GAAG,EAAG;gBAChDiD,GAAG,EAAEnD,MAAM,CAAC5C,KAAM;gBAClBqG,OAAO,EAAGhF,CAAC,IAAK;kBACdA,CAAC,CAACG,MAAM,CAAC8E,KAAK,CAACC,OAAO,GAAG,MAAM;gBACjC;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA,GAvDOxC,MAAM,CAACJ,GAAG;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwDf,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnG,EAAA,CA/ZID,WAAW;EAAA,QACEL,WAAW,EACXC,OAAO;AAAA;AAAA4H,EAAA,GAFpBxH,WAAW;AAiajB,eAAeA,WAAW;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}