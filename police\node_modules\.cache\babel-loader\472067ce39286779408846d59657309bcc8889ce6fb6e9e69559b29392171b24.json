{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\POlice\\\\police\\\\src\\\\component\\\\FilterModal.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport './FilterModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FilterModal = ({\n  isOpen,\n  onClose,\n  selectedCategory,\n  selectedProvince,\n  onCategoryChange,\n  onProvinceChange,\n  onApply\n}) => {\n  _s();\n  const [startDate, setStartDate] = React.useState('');\n  const [endDate, setEndDate] = React.useState('');\n  if (!isOpen) return null;\n  const newsCategories = ['all', 'fire', 'cyber crime', 'informative', 'harmful weapon', 'flood landslide', 'organizational program', 'gambling', 'dead bodies found', 'rape', 'home ministry program', 'Narcotic', 'IGP program', 'blackmailing', 'Quarrel/Disturbance', 'Bribery', 'drug', 'violence', 'Suspicious thing', 'crime report', 'burglary', 'pick pocketing', 'harassment', 'illegal trading', 'police day program', 'misbehaviour', 'robbery', 'public gathering', 'crime(arrest)', 'human trafficking', 'miscellaneous'];\n  const provinces = ['All Provinces', 'Province 1', 'Madhesh Province', 'Bagmati Province', 'Gandaki Province', 'Lumbini Province', 'Karnali Province', 'Sudurpashchim Province'];\n  const getCategoryDisplayName = category => {\n    if (category === 'all') return 'All Categories';\n    return category.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');\n  };\n  const handleApply = () => {\n    onApply();\n    onClose();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"filter-modal-overlay\",\n    onClick: onClose,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"filter-modal-card\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"filter-close-btn\",\n          onClick: onClose,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Select Filters\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"filter-save-btn\",\n          onClick: handleApply,\n          children: \"Apply\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Select Date Range\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"date-picker-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"date-input-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Start Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: startDate,\n                onChange: e => setStartDate(e.target.value),\n                className: \"date-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"date-input-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"End Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: endDate,\n                onChange: e => setEndDate(e.target.value),\n                className: \"date-input\",\n                min: startDate\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Province\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-options-grid\",\n            children: provinces.map(province => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `filter-option-btn ${selectedProvince === province ? 'active' : ''}`,\n              onClick: () => onProvinceChange(province),\n              children: province\n            }, province, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n};\n_s(FilterModal, \"tOzGTxBi4OgONuhYAP43LC7m7Tg=\");\n_c = FilterModal;\nexport default FilterModal;\nvar _c;\n$RefreshReg$(_c, \"FilterModal\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "FilterModal", "isOpen", "onClose", "selectedCate<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onCategoryChange", "onProvinceChange", "onApply", "_s", "startDate", "setStartDate", "useState", "endDate", "setEndDate", "newsCategories", "provinces", "getCategoryDisplayName", "category", "split", "map", "word", "char<PERSON>t", "toUpperCase", "slice", "join", "handleApply", "className", "onClick", "children", "e", "stopPropagation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "value", "onChange", "target", "min", "province", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/POlice/police/src/component/FilterModal.js"], "sourcesContent": ["import React from 'react';\nimport './FilterModal.css';\n\nconst FilterModal = ({\n  isOpen,\n  onClose,\n  selectedCategory,\n  selectedProvince,\n  onCategoryChange,\n  onProvinceChange,\n  onApply\n}) => {\n  const [startDate, setStartDate] = React.useState('');\n  const [endDate, setEndDate] = React.useState('');\n\n  if (!isOpen) return null;\n\n  const newsCategories = [\n    'all',\n    'fire',\n    'cyber crime',\n    'informative',\n    'harmful weapon',\n    'flood landslide',\n    'organizational program',\n    'gambling',\n    'dead bodies found',\n    'rape',\n    'home ministry program',\n    'Narcotic',\n    'IGP program',\n    'blackmailing',\n    'Quarrel/Disturbance',\n    'Bribery',\n    'drug',\n    'violence',\n    'Suspicious thing',\n    'crime report',\n    'burglary',\n    'pick pocketing',\n    'harassment',\n    'illegal trading',\n    'police day program',\n    'misbehaviour',\n    'robbery',\n    'public gathering',\n    'crime(arrest)',\n    'human trafficking',\n    'miscellaneous'\n  ];\n\n  const provinces = [\n    'All Provinces',\n    'Province 1',\n    'Madhesh Province',\n    'Bagmati Province',\n    'Gandaki Province',\n    'Lumbini Province',\n    'Karnali Province',\n    'Sudurpashchim Province'\n  ];\n\n  const getCategoryDisplayName = (category) => {\n    if (category === 'all') return 'All Categories';\n    return category.split(' ').map(word => \n      word.charAt(0).toUpperCase() + word.slice(1)\n    ).join(' ');\n  };\n\n  const handleApply = () => {\n    onApply();\n    onClose();\n  };\n\n  return (\n    <div className=\"filter-modal-overlay\" onClick={onClose}>\n      <div className=\"filter-modal-card\" onClick={(e) => e.stopPropagation()}>\n        <div className=\"filter-modal-header\">\n          <button className=\"filter-close-btn\" onClick={onClose}>×</button>\n          <h2>Select Filters</h2>\n          <button className=\"filter-save-btn\" onClick={handleApply}>Apply</button>\n        </div>\n\n        <div className=\"filter-modal-content\">\n          <div className=\"filter-section\">\n            <h3>Select Date Range</h3>\n            <div className=\"date-picker-section\">\n              <div className=\"date-input-group\">\n                <label>Start Date</label>\n                <input\n                  type=\"date\"\n                  value={startDate}\n                  onChange={(e) => setStartDate(e.target.value)}\n                  className=\"date-input\"\n                />\n              </div>\n              <div className=\"date-input-group\">\n                <label>End Date</label>\n                <input\n                  type=\"date\"\n                  value={endDate}\n                  onChange={(e) => setEndDate(e.target.value)}\n                  className=\"date-input\"\n                  min={startDate}\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"filter-section\">\n            <h3>Province</h3>\n            <div className=\"filter-options-grid\">\n              {provinces.map(province => (\n                <button\n                  key={province}\n                  className={`filter-option-btn ${selectedProvince === province ? 'active' : ''}`}\n                  onClick={() => onProvinceChange(province)}\n                >\n                  {province}\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FilterModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EACnBC,MAAM;EACNC,OAAO;EACPC,gBAAgB;EAChBC,gBAAgB;EAChBC,gBAAgB;EAChBC,gBAAgB;EAChBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGb,KAAK,CAACc,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhB,KAAK,CAACc,QAAQ,CAAC,EAAE,CAAC;EAEhD,IAAI,CAACV,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMa,cAAc,GAAG,CACrB,KAAK,EACL,MAAM,EACN,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,iBAAiB,EACjB,wBAAwB,EACxB,UAAU,EACV,mBAAmB,EACnB,MAAM,EACN,uBAAuB,EACvB,UAAU,EACV,aAAa,EACb,cAAc,EACd,qBAAqB,EACrB,SAAS,EACT,MAAM,EACN,UAAU,EACV,kBAAkB,EAClB,cAAc,EACd,UAAU,EACV,gBAAgB,EAChB,YAAY,EACZ,iBAAiB,EACjB,oBAAoB,EACpB,cAAc,EACd,SAAS,EACT,kBAAkB,EAClB,eAAe,EACf,mBAAmB,EACnB,eAAe,CAChB;EAED,MAAMC,SAAS,GAAG,CAChB,eAAe,EACf,YAAY,EACZ,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,wBAAwB,CACzB;EAED,MAAMC,sBAAsB,GAAIC,QAAQ,IAAK;IAC3C,IAAIA,QAAQ,KAAK,KAAK,EAAE,OAAO,gBAAgB;IAC/C,OAAOA,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,IAAI,IACjCA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAC7C,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACb,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBlB,OAAO,CAAC,CAAC;IACTL,OAAO,CAAC,CAAC;EACX,CAAC;EAED,oBACEH,OAAA;IAAK2B,SAAS,EAAC,sBAAsB;IAACC,OAAO,EAAEzB,OAAQ;IAAA0B,QAAA,eACrD7B,OAAA;MAAK2B,SAAS,EAAC,mBAAmB;MAACC,OAAO,EAAGE,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;MAAAF,QAAA,gBACrE7B,OAAA;QAAK2B,SAAS,EAAC,qBAAqB;QAAAE,QAAA,gBAClC7B,OAAA;UAAQ2B,SAAS,EAAC,kBAAkB;UAACC,OAAO,EAAEzB,OAAQ;UAAA0B,QAAA,EAAC;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACjEnC,OAAA;UAAA6B,QAAA,EAAI;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBnC,OAAA;UAAQ2B,SAAS,EAAC,iBAAiB;UAACC,OAAO,EAAEF,WAAY;UAAAG,QAAA,EAAC;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,eAENnC,OAAA;QAAK2B,SAAS,EAAC,sBAAsB;QAAAE,QAAA,gBACnC7B,OAAA;UAAK2B,SAAS,EAAC,gBAAgB;UAAAE,QAAA,gBAC7B7B,OAAA;YAAA6B,QAAA,EAAI;UAAiB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BnC,OAAA;YAAK2B,SAAS,EAAC,qBAAqB;YAAAE,QAAA,gBAClC7B,OAAA;cAAK2B,SAAS,EAAC,kBAAkB;cAAAE,QAAA,gBAC/B7B,OAAA;gBAAA6B,QAAA,EAAO;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzBnC,OAAA;gBACEoC,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAE3B,SAAU;gBACjB4B,QAAQ,EAAGR,CAAC,IAAKnB,YAAY,CAACmB,CAAC,CAACS,MAAM,CAACF,KAAK,CAAE;gBAC9CV,SAAS,EAAC;cAAY;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnC,OAAA;cAAK2B,SAAS,EAAC,kBAAkB;cAAAE,QAAA,gBAC/B7B,OAAA;gBAAA6B,QAAA,EAAO;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvBnC,OAAA;gBACEoC,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAExB,OAAQ;gBACfyB,QAAQ,EAAGR,CAAC,IAAKhB,UAAU,CAACgB,CAAC,CAACS,MAAM,CAACF,KAAK,CAAE;gBAC5CV,SAAS,EAAC,YAAY;gBACtBa,GAAG,EAAE9B;cAAU;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnC,OAAA;UAAK2B,SAAS,EAAC,gBAAgB;UAAAE,QAAA,gBAC7B7B,OAAA;YAAA6B,QAAA,EAAI;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBnC,OAAA;YAAK2B,SAAS,EAAC,qBAAqB;YAAAE,QAAA,EACjCb,SAAS,CAACI,GAAG,CAACqB,QAAQ,iBACrBzC,OAAA;cAEE2B,SAAS,EAAE,qBAAqBtB,gBAAgB,KAAKoC,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;cAChFb,OAAO,EAAEA,CAAA,KAAMrB,gBAAgB,CAACkC,QAAQ,CAAE;cAAAZ,QAAA,EAEzCY;YAAQ,GAJJA,QAAQ;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKP,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1B,EAAA,CA5HIR,WAAW;AAAAyC,EAAA,GAAXzC,WAAW;AA8HjB,eAAeA,WAAW;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}