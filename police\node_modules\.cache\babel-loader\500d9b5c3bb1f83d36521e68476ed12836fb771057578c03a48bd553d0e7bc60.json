{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\POlice\\\\police\\\\src\\\\component\\\\NoticeDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport './Notice.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NoticeDetail = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    id\n  } = useParams();\n  const [notice, setNotice] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [liked, setLiked] = useState(false);\n  const fetchNoticeDetail = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`http://localhost:5000/api/notice/${id}`, {\n        withCredentials: true\n      });\n      setNotice(response.data);\n    } catch (err) {\n      setError('Failed to fetch notice details');\n      console.error('Error fetching notice detail:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, [id]);\n  useEffect(() => {\n    if (id) {\n      fetchNoticeDetail();\n    }\n  }, [id, fetchNoticeDetail]);\n  const handleBack = () => {\n    navigate('/notice');\n  };\n  const handleLike = async () => {\n    try {\n      const response = await axios.post(`http://localhost:5000/api/notice/${id}/like`, {}, {\n        withCredentials: true\n      });\n      setNotice(prev => ({\n        ...prev,\n        likes: response.data.likes\n      }));\n      setLiked(true);\n    } catch (err) {\n      console.error('Error liking notice:', err);\n    }\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n\n    // Simple B.S. conversion (approximate)\n    const bsYear = year + 57;\n    return `${year}-${month}-${day} (${bsYear}-${month}-${day} B.S)`;\n  };\n  const getCategoryDisplayName = category => {\n    return category.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"notice-detail-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notice-detail-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notice-detail-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"notice-back-btn\",\n            onClick: handleBack,\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\",\n                fill: \"white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"Notice Detail\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '40px',\n              height: '40px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notice-detail-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notice-loading\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading notice...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !notice) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"notice-detail-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notice-detail-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notice-detail-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"notice-back-btn\",\n            onClick: handleBack,\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\",\n                fill: \"white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"Notice Detail\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '40px',\n              height: '40px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notice-detail-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notice-error\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: error || 'Notice not found'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: fetchNoticeDetail,\n              className: \"retry-btn\",\n              children: \"Retry\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"notice-detail-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"notice-detail-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notice-detail-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"notice-back-btn\",\n          onClick: handleBack,\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"24\",\n            height: \"24\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\",\n              fill: \"white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Notice Detail\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '40px',\n            height: '40px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notice-detail-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notice-detail-meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notice-detail-date\",\n            children: formatDate(notice.createdAt)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notice-detail-category\",\n            children: getCategoryDisplayName(notice.category)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"notice-detail-title\",\n          children: notice.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), notice.image && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notice-detail-image\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `http://localhost:5000${notice.image.url}`,\n            alt: notice.title,\n            onError: e => {\n              e.target.parentElement.style.display = 'none';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notice-detail-text\",\n          children: notice.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"notice-detail-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `like-btn ${liked ? 'liked' : ''}`,\n            onClick: handleLike,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              fill: liked ? 'currentColor' : 'none',\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), notice.likes || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notice-stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), notice.views || 0, \" views\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), notice.createdBy && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notice-stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"7\",\n                r: \"4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this), \"By \", notice.createdBy.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this), notice.province && notice.province !== 'All Provinces' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notice-stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"10\",\n                r: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), notice.province]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n};\n_s(NoticeDetail, \"FKrYqoLpD7dZWHaws9t717UEB+w=\", false, function () {\n  return [useNavigate, useParams];\n});\n_c = NoticeDetail;\nexport default NoticeDetail;\nvar _c;\n$RefreshReg$(_c, \"NoticeDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useNavigate", "useParams", "axios", "jsxDEV", "_jsxDEV", "NoticeDetail", "_s", "navigate", "id", "notice", "setNotice", "loading", "setLoading", "error", "setError", "liked", "setLiked", "fetchNoticeDetail", "response", "get", "withCredentials", "data", "err", "console", "handleBack", "handleLike", "post", "prev", "likes", "formatDate", "dateString", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "bsYear", "getCategoryDisplayName", "category", "split", "map", "word", "char<PERSON>t", "toUpperCase", "slice", "join", "className", "children", "onClick", "width", "height", "viewBox", "d", "fill", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "createdAt", "title", "image", "src", "url", "alt", "onError", "e", "target", "parentElement", "display", "content", "stroke", "cx", "cy", "r", "views", "created<PERSON>y", "name", "province", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/POlice/police/src/component/NoticeDetail.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport './Notice.css';\n\nconst NoticeDetail = () => {\n  const navigate = useNavigate();\n  const { id } = useParams();\n  const [notice, setNotice] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [liked, setLiked] = useState(false);\n\n  const fetchNoticeDetail = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`http://localhost:5000/api/notice/${id}`, {\n        withCredentials: true\n      });\n      setNotice(response.data);\n    } catch (err) {\n      setError('Failed to fetch notice details');\n      console.error('Error fetching notice detail:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, [id]);\n\n  useEffect(() => {\n    if (id) {\n      fetchNoticeDetail();\n    }\n  }, [id, fetchNoticeDetail]);\n\n  const handleBack = () => {\n    navigate('/notice');\n  };\n\n  const handleLike = async () => {\n    try {\n      const response = await axios.post(`http://localhost:5000/api/notice/${id}/like`, {}, {\n        withCredentials: true\n      });\n      setNotice(prev => ({\n        ...prev,\n        likes: response.data.likes\n      }));\n      setLiked(true);\n    } catch (err) {\n      console.error('Error liking notice:', err);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    \n    // Simple B.S. conversion (approximate)\n    const bsYear = year + 57;\n    return `${year}-${month}-${day} (${bsYear}-${month}-${day} B.S)`;\n  };\n\n  const getCategoryDisplayName = (category) => {\n    return category.split(' ').map(word => \n      word.charAt(0).toUpperCase() + word.slice(1)\n    ).join(' ');\n  };\n\n  if (loading) {\n    return (\n      <div className=\"notice-detail-container\">\n        <div className=\"notice-detail-card\">\n          <div className=\"notice-detail-header\">\n            <button className=\"notice-back-btn\" onClick={handleBack}>\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\">\n                <path d=\"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\" fill=\"white\"/>\n              </svg>\n            </button>\n            <h1>Notice Detail</h1>\n            <div style={{ width: '40px', height: '40px' }}></div>\n          </div>\n          <div className=\"notice-detail-content\">\n            <div className=\"notice-loading\">\n              <div className=\"loading-spinner\"></div>\n              <p>Loading notice...</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !notice) {\n    return (\n      <div className=\"notice-detail-container\">\n        <div className=\"notice-detail-card\">\n          <div className=\"notice-detail-header\">\n            <button className=\"notice-back-btn\" onClick={handleBack}>\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\">\n                <path d=\"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\" fill=\"white\"/>\n              </svg>\n            </button>\n            <h1>Notice Detail</h1>\n            <div style={{ width: '40px', height: '40px' }}></div>\n          </div>\n          <div className=\"notice-detail-content\">\n            <div className=\"notice-error\">\n              <p>{error || 'Notice not found'}</p>\n              <button onClick={fetchNoticeDetail} className=\"retry-btn\">Retry</button>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"notice-detail-container\">\n      <div className=\"notice-detail-card\">\n        <div className=\"notice-detail-header\">\n          <button className=\"notice-back-btn\" onClick={handleBack}>\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\">\n              <path d=\"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\" fill=\"white\"/>\n            </svg>\n          </button>\n          <h1>Notice Detail</h1>\n          <div style={{ width: '40px', height: '40px' }}></div>\n        </div>\n\n        <div className=\"notice-detail-content\">\n          <div className=\"notice-detail-meta\">\n            <div className=\"notice-detail-date\">\n              {formatDate(notice.createdAt)}\n            </div>\n            <div className=\"notice-detail-category\">\n              {getCategoryDisplayName(notice.category)}\n            </div>\n          </div>\n\n          <h2 className=\"notice-detail-title\">{notice.title}</h2>\n\n          {notice.image && (\n            <div className=\"notice-detail-image\">\n              <img \n                src={`http://localhost:5000${notice.image.url}`} \n                alt={notice.title}\n                onError={(e) => {\n                  e.target.parentElement.style.display = 'none';\n                }}\n              />\n            </div>\n          )}\n\n          <div className=\"notice-detail-text\">\n            {notice.content}\n          </div>\n\n          <div className=\"notice-detail-stats\">\n            <button \n              className={`like-btn ${liked ? 'liked' : ''}`}\n              onClick={handleLike}\n            >\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill={liked ? 'currentColor' : 'none'} stroke=\"currentColor\">\n                <path d=\"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"/>\n              </svg>\n              {notice.likes || 0}\n            </button>\n\n            <div className=\"notice-stat\">\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                <path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"/>\n                <circle cx=\"12\" cy=\"12\" r=\"3\"/>\n              </svg>\n              {notice.views || 0} views\n            </div>\n\n            {notice.createdBy && (\n              <div className=\"notice-stat\">\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                  <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"/>\n                  <circle cx=\"12\" cy=\"7\" r=\"4\"/>\n                </svg>\n                By {notice.createdBy.name}\n              </div>\n            )}\n\n            {notice.province && notice.province !== 'All Provinces' && (\n              <div className=\"notice-stat\">\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                  <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"/>\n                  <circle cx=\"12\" cy=\"10\" r=\"3\"/>\n                </svg>\n                {notice.province}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default NoticeDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEQ;EAAG,CAAC,GAAGP,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACQ,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAEzC,MAAMoB,iBAAiB,GAAGlB,WAAW,CAAC,YAAY;IAChD,IAAI;MACFa,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAMhB,KAAK,CAACiB,GAAG,CAAC,oCAAoCX,EAAE,EAAE,EAAE;QACzEY,eAAe,EAAE;MACnB,CAAC,CAAC;MACFV,SAAS,CAACQ,QAAQ,CAACG,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZR,QAAQ,CAAC,gCAAgC,CAAC;MAC1CS,OAAO,CAACV,KAAK,CAAC,+BAA+B,EAAES,GAAG,CAAC;IACrD,CAAC,SAAS;MACRV,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACJ,EAAE,CAAC,CAAC;EAERV,SAAS,CAAC,MAAM;IACd,IAAIU,EAAE,EAAE;MACNS,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACT,EAAE,EAAES,iBAAiB,CAAC,CAAC;EAE3B,MAAMO,UAAU,GAAGA,CAAA,KAAM;IACvBjB,QAAQ,CAAC,SAAS,CAAC;EACrB,CAAC;EAED,MAAMkB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMhB,KAAK,CAACwB,IAAI,CAAC,oCAAoClB,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE;QACnFY,eAAe,EAAE;MACnB,CAAC,CAAC;MACFV,SAAS,CAACiB,IAAI,KAAK;QACjB,GAAGA,IAAI;QACPC,KAAK,EAAEV,QAAQ,CAACG,IAAI,CAACO;MACvB,CAAC,CAAC,CAAC;MACHZ,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOM,GAAG,EAAE;MACZC,OAAO,CAACV,KAAK,CAAC,sBAAsB,EAAES,GAAG,CAAC;IAC5C;EACF,CAAC;EAED,MAAMO,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,IAAI,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;;IAEnD;IACA,MAAMG,MAAM,GAAGR,IAAI,GAAG,EAAE;IACxB,OAAO,GAAGA,IAAI,IAAIE,KAAK,IAAII,GAAG,KAAKE,MAAM,IAAIN,KAAK,IAAII,GAAG,OAAO;EAClE,CAAC;EAED,MAAMG,sBAAsB,GAAIC,QAAQ,IAAK;IAC3C,OAAOA,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,IAAI,IACjCA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAC7C,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACb,CAAC;EAED,IAAIvC,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK+C,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtChD,OAAA;QAAK+C,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjChD,OAAA;UAAK+C,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnChD,OAAA;YAAQ+C,SAAS,EAAC,iBAAiB;YAACE,OAAO,EAAE7B,UAAW;YAAA4B,QAAA,eACtDhD,OAAA;cAAKkD,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAC7ChD,OAAA;gBAAMqD,CAAC,EAAC,8DAA8D;gBAACC,IAAI,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACT1D,OAAA;YAAAgD,QAAA,EAAI;UAAa;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtB1D,OAAA;YAAK2D,KAAK,EAAE;cAAET,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACN1D,OAAA;UAAK+C,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpChD,OAAA;YAAK+C,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BhD,OAAA;cAAK+C,SAAS,EAAC;YAAiB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvC1D,OAAA;cAAAgD,QAAA,EAAG;YAAiB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIjD,KAAK,IAAI,CAACJ,MAAM,EAAE;IACpB,oBACEL,OAAA;MAAK+C,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtChD,OAAA;QAAK+C,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjChD,OAAA;UAAK+C,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnChD,OAAA;YAAQ+C,SAAS,EAAC,iBAAiB;YAACE,OAAO,EAAE7B,UAAW;YAAA4B,QAAA,eACtDhD,OAAA;cAAKkD,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAC7ChD,OAAA;gBAAMqD,CAAC,EAAC,8DAA8D;gBAACC,IAAI,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACT1D,OAAA;YAAAgD,QAAA,EAAI;UAAa;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtB1D,OAAA;YAAK2D,KAAK,EAAE;cAAET,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACN1D,OAAA;UAAK+C,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpChD,OAAA;YAAK+C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BhD,OAAA;cAAAgD,QAAA,EAAIvC,KAAK,IAAI;YAAkB;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpC1D,OAAA;cAAQiD,OAAO,EAAEpC,iBAAkB;cAACkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE1D,OAAA;IAAK+C,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtChD,OAAA;MAAK+C,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjChD,OAAA;QAAK+C,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnChD,OAAA;UAAQ+C,SAAS,EAAC,iBAAiB;UAACE,OAAO,EAAE7B,UAAW;UAAA4B,QAAA,eACtDhD,OAAA;YAAKkD,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC7ChD,OAAA;cAAMqD,CAAC,EAAC,8DAA8D;cAACC,IAAI,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACT1D,OAAA;UAAAgD,QAAA,EAAI;QAAa;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtB1D,OAAA;UAAK2D,KAAK,EAAE;YAAET,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAO;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eAEN1D,OAAA;QAAK+C,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpChD,OAAA;UAAK+C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjChD,OAAA;YAAK+C,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAChCvB,UAAU,CAACpB,MAAM,CAACuD,SAAS;UAAC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACN1D,OAAA;YAAK+C,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EACpCV,sBAAsB,CAACjC,MAAM,CAACkC,QAAQ;UAAC;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1D,OAAA;UAAI+C,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAE3C,MAAM,CAACwD;QAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAEtDrD,MAAM,CAACyD,KAAK,iBACX9D,OAAA;UAAK+C,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClChD,OAAA;YACE+D,GAAG,EAAE,wBAAwB1D,MAAM,CAACyD,KAAK,CAACE,GAAG,EAAG;YAChDC,GAAG,EAAE5D,MAAM,CAACwD,KAAM;YAClBK,OAAO,EAAGC,CAAC,IAAK;cACdA,CAAC,CAACC,MAAM,CAACC,aAAa,CAACV,KAAK,CAACW,OAAO,GAAG,MAAM;YAC/C;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAED1D,OAAA;UAAK+C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAChC3C,MAAM,CAACkE;QAAO;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEN1D,OAAA;UAAK+C,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClChD,OAAA;YACE+C,SAAS,EAAE,YAAYpC,KAAK,GAAG,OAAO,GAAG,EAAE,EAAG;YAC9CsC,OAAO,EAAE5B,UAAW;YAAA2B,QAAA,gBAEpBhD,OAAA;cAAKkD,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACE,IAAI,EAAE3C,KAAK,GAAG,cAAc,GAAG,MAAO;cAAC6D,MAAM,EAAC,cAAc;cAAAxB,QAAA,eAC1GhD,OAAA;gBAAMqD,CAAC,EAAC;cAA0I;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjJ,CAAC,EACLrD,MAAM,CAACmB,KAAK,IAAI,CAAC;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eAET1D,OAAA;YAAK+C,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BhD,OAAA;cAAKkD,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACE,IAAI,EAAC,MAAM;cAACkB,MAAM,EAAC,cAAc;cAAAxB,QAAA,gBAC/EhD,OAAA;gBAAMqD,CAAC,EAAC;cAA8C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eACxD1D,OAAA;gBAAQyE,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,EACLrD,MAAM,CAACuE,KAAK,IAAI,CAAC,EAAC,QACrB;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EAELrD,MAAM,CAACwE,SAAS,iBACf7E,OAAA;YAAK+C,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BhD,OAAA;cAAKkD,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACE,IAAI,EAAC,MAAM;cAACkB,MAAM,EAAC,cAAc;cAAAxB,QAAA,gBAC/EhD,OAAA;gBAAMqD,CAAC,EAAC;cAA2C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eACrD1D,OAAA;gBAAQyE,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,CAAC,EAAC;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,OACH,EAACrD,MAAM,CAACwE,SAAS,CAACC,IAAI;UAAA;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CACN,EAEArD,MAAM,CAAC0E,QAAQ,IAAI1E,MAAM,CAAC0E,QAAQ,KAAK,eAAe,iBACrD/E,OAAA;YAAK+C,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BhD,OAAA;cAAKkD,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACE,IAAI,EAAC,MAAM;cAACkB,MAAM,EAAC,cAAc;cAAAxB,QAAA,gBAC/EhD,OAAA;gBAAMqD,CAAC,EAAC;cAAgD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAC1D1D,OAAA;gBAAQyE,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,EACLrD,MAAM,CAAC0E,QAAQ;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxD,EAAA,CArMID,YAAY;EAAA,QACCL,WAAW,EACbC,SAAS;AAAA;AAAAmF,EAAA,GAFpB/E,YAAY;AAuMlB,eAAeA,YAAY;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}