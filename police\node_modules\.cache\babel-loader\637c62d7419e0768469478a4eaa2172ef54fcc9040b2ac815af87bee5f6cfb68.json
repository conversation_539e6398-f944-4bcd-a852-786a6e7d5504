{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\POlice\\\\police\\\\src\\\\component\\\\Settings.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport homeIcon from '../assets/home.png';\nimport publicEyeIcon from '../assets/public-eye.png';\nimport settingsIcon from '../assets/settings.png';\nimport logo from '../assets/logo.png';\nimport './Settings.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Settings = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [biometricEnabled, setBiometricEnabled] = useState(false);\n  const [notificationsEnabled, setNotificationsEnabled] = useState(true);\n  const [panicModeEnabled, setPanicModeEnabled] = useState(false);\n  const [selectedLanguage, setSelectedLanguage] = useState('English');\n  const [selectedTheme, setSelectedTheme] = useState('Light');\n  const handleNavigation = path => {\n    navigate(path);\n  };\n  const handleEditProfile = () => {\n    // Navigate to profile edit page\n    console.log('Edit profile clicked');\n  };\n  const handleChangePassword = () => {\n    // Navigate to change password page\n    console.log('Change password clicked');\n  };\n  const handleLanguageChange = () => {\n    // Show language selection modal\n    console.log('Language change clicked');\n  };\n  const handleThemeChange = () => {\n    // Toggle theme\n    setSelectedTheme(selectedTheme === 'Light' ? 'Dark' : 'Light');\n  };\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"settings-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"settings-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-profile-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-profile-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: logo,\n              alt: \"Profile\",\n              className: \"profile-avatar\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: (user === null || user === void 0 ? void 0 : user.name) || 'Ujjal Basnet'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: (user === null || user === void 0 ? void 0 : user.email) || '<EMAIL>'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"edit-profile-btn\",\n            onClick: handleEditProfile,\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"User Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            onClick: handleChangePassword,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"24\",\n                height: \"24\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM12 17c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zM15.1 8H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"setting-label\",\n              children: \"Change Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"24\",\n                height: \"24\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M17.81 4.47c-.08 0-.16-.02-.23-.06C15.66 3.42 14 3 12.01 3c-1.98 0-3.86.47-5.57 1.41-.24.13-.54.04-.68-.2-.13-.24-.04-.55.2-.68C7.82 2.52 9.86 2 12.01 2c2.13 0 3.99.47 6.03 1.52.25.13.34.43.21.67-.09.18-.26.28-.44.28zM3.5 9.72c-.1 0-.2-.03-.29-.09-.23-.16-.28-.47-.12-.7.99-1.4 2.25-2.5 3.75-3.27C9.98 4.04 14 4.03 17.15 5.65c1.5.77 2.76 1.86 3.75 3.27.16.22.11.54-.12.7-.23.16-.54.11-.7-.12-.9-1.29-2.04-2.25-3.39-2.94-2.87-1.47-6.54-1.47-9.4.01-1.36.69-2.5 1.65-3.4 2.94-.08.14-.23.21-.39.21z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 88,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"setting-label\",\n              children: \"Biometric Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-toggle\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"toggle-switch\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: biometricEnabled,\n                  onChange: e => setBiometricEnabled(e.target.checked)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 94,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"toggle-slider\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"24\",\n                height: \"24\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"setting-label\",\n              children: \"Notifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-toggle\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"toggle-switch\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: notificationsEnabled,\n                  onChange: e => setNotificationsEnabled(e.target.checked)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"toggle-slider\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panic-mode-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"setting-label\",\n              children: \"Panic Mode\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-toggle\",\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"toggle-switch\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: panicModeEnabled,\n                  onChange: e => setPanicModeEnabled(e.target.checked)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"toggle-slider\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"panic-mode-note\",\n            children: \"Note: Please turn off or disable battery saver mode while panic mode is enabled. Also please do not swipe off and close app from memory for better user experience.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"App Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            onClick: handleLanguageChange,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"24\",\n                height: \"24\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"setting-label\",\n              children: \"Language\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"setting-value\",\n              children: selectedLanguage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            onClick: handleThemeChange,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"24\",\n                height: \"24\",\n                viewBox: \"0 0 24 24\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9c.83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8zm-5.5 9c-.83 0-1.5-.67-1.5-1.5S5.67 9 6.5 9 8 9.67 8 10.5 7.33 12 6.5 12zm3-4C8.67 8 8 7.33 8 6.5S8.67 5 9.5 5s1.5.67 1.5 1.5S10.33 8 9.5 8zm5 0c-.83 0-1.5-.67-1.5-1.5S13.67 5 14.5 5s1.5.67 1.5 1.5S15.33 8 14.5 8zm3 4c-.83 0-1.5-.67-1.5-1.5S16.67 9 17.5 9s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"setting-label\",\n              children: \"Theme\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"setting-value\",\n              children: selectedTheme\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logout-section\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"logout-btn\",\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), \"Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bottom-nav\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"nav-btn\",\n          onClick: () => handleNavigation('/home'),\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: homeIcon,\n            alt: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"nav-btn\",\n          onClick: () => handleNavigation('/public-eye'),\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: publicEyeIcon,\n            alt: \"Public Eye\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Public Eye\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"nav-btn active\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: settingsIcon,\n            alt: \"Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(Settings, \"N6rG3wCNc9bpLde8mrz0OQPwq8c=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useAuth", "homeIcon", "publicEyeIcon", "settingsIcon", "logo", "jsxDEV", "_jsxDEV", "Settings", "_s", "navigate", "user", "logout", "biometricEnabled", "setBiometricEnabled", "notificationsEnabled", "setNotificationsEnabled", "panicModeEnabled", "setPanicModeEnabled", "selectedLanguage", "setSelectedLanguage", "selectedTheme", "setSelectedTheme", "handleNavigation", "path", "handleEditProfile", "console", "log", "handleChangePassword", "handleLanguageChange", "handleThemeChange", "handleLogout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "name", "email", "onClick", "width", "height", "viewBox", "fill", "d", "type", "checked", "onChange", "e", "target", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/POlice/police/src/component/Settings.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport homeIcon from '../assets/home.png';\nimport publicEyeIcon from '../assets/public-eye.png';\nimport settingsIcon from '../assets/settings.png';\nimport logo from '../assets/logo.png';\nimport './Settings.css';\n\nconst Settings = () => {\n  const navigate = useNavigate();\n  const { user, logout } = useAuth();\n  const [biometricEnabled, setBiometricEnabled] = useState(false);\n  const [notificationsEnabled, setNotificationsEnabled] = useState(true);\n  const [panicModeEnabled, setPanicModeEnabled] = useState(false);\n  const [selectedLanguage, setSelectedLanguage] = useState('English');\n  const [selectedTheme, setSelectedTheme] = useState('Light');\n\n  const handleNavigation = (path) => {\n    navigate(path);\n  };\n\n  const handleEditProfile = () => {\n    // Navigate to profile edit page\n    console.log('Edit profile clicked');\n  };\n\n  const handleChangePassword = () => {\n    // Navigate to change password page\n    console.log('Change password clicked');\n  };\n\n  const handleLanguageChange = () => {\n    // Show language selection modal\n    console.log('Language change clicked');\n  };\n\n  const handleThemeChange = () => {\n    // Toggle theme\n    setSelectedTheme(selectedTheme === 'Light' ? 'Dark' : 'Light');\n  };\n\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  return (\n    <div className=\"settings-container\">\n      <div className=\"settings-card\">\n        <div className=\"settings-header\">\n          <h1>Settings</h1>\n        </div>\n\n        <div className=\"settings-content\">\n          {/* User Profile Section */}\n          <div className=\"user-profile-section\">\n            <div className=\"user-profile-info\">\n              <img src={logo} alt=\"Profile\" className=\"profile-avatar\" />\n              <div className=\"user-details\">\n                <h3>{user?.name || 'Ujjal Basnet'}</h3>\n                <p>{user?.email || '<EMAIL>'}</p>\n              </div>\n            </div>\n            <button className=\"edit-profile-btn\" onClick={handleEditProfile}>\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"/>\n              </svg>\n            </button>\n          </div>\n\n          {/* User Settings Section */}\n          <div className=\"settings-section\">\n            <h2>User Settings</h2>\n            \n            <div className=\"setting-item\" onClick={handleChangePassword}>\n              <div className=\"setting-icon\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM12 17c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zM15.1 8H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z\"/>\n                </svg>\n              </div>\n              <span className=\"setting-label\">Change Password</span>\n            </div>\n\n            <div className=\"setting-item\">\n              <div className=\"setting-icon\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M17.81 4.47c-.08 0-.16-.02-.23-.06C15.66 3.42 14 3 12.01 3c-1.98 0-3.86.47-5.57 1.41-.24.13-.54.04-.68-.2-.13-.24-.04-.55.2-.68C7.82 2.52 9.86 2 12.01 2c2.13 0 3.99.47 6.03 1.52.25.13.34.43.21.67-.09.18-.26.28-.44.28zM3.5 9.72c-.1 0-.2-.03-.29-.09-.23-.16-.28-.47-.12-.7.99-1.4 2.25-2.5 3.75-3.27C9.98 4.04 14 4.03 17.15 5.65c1.5.77 2.76 1.86 3.75 3.27.16.22.11.54-.12.7-.23.16-.54.11-.7-.12-.9-1.29-2.04-2.25-3.39-2.94-2.87-1.47-6.54-1.47-9.4.01-1.36.69-2.5 1.65-3.4 2.94-.08.14-.23.21-.39.21z\"/>\n                </svg>\n              </div>\n              <span className=\"setting-label\">Biometric Login</span>\n              <div className=\"setting-toggle\">\n                <label className=\"toggle-switch\">\n                  <input\n                    type=\"checkbox\"\n                    checked={biometricEnabled}\n                    onChange={(e) => setBiometricEnabled(e.target.checked)}\n                  />\n                  <span className=\"toggle-slider\"></span>\n                </label>\n              </div>\n            </div>\n\n            <div className=\"setting-item\">\n              <div className=\"setting-icon\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z\"/>\n                </svg>\n              </div>\n              <span className=\"setting-label\">Notifications</span>\n              <div className=\"setting-toggle\">\n                <label className=\"toggle-switch\">\n                  <input\n                    type=\"checkbox\"\n                    checked={notificationsEnabled}\n                    onChange={(e) => setNotificationsEnabled(e.target.checked)}\n                  />\n                  <span className=\"toggle-slider\"></span>\n                </label>\n              </div>\n            </div>\n          </div>\n\n          {/* Panic Mode Section */}\n          <div className=\"panic-mode-section\">\n            <div className=\"setting-item\">\n              <span className=\"setting-label\">Panic Mode</span>\n              <div className=\"setting-toggle\">\n                <label className=\"toggle-switch\">\n                  <input\n                    type=\"checkbox\"\n                    checked={panicModeEnabled}\n                    onChange={(e) => setPanicModeEnabled(e.target.checked)}\n                  />\n                  <span className=\"toggle-slider\"></span>\n                </label>\n              </div>\n            </div>\n            <p className=\"panic-mode-note\">\n              Note: Please turn off or disable battery saver mode while panic mode is enabled. \n              Also please do not swipe off and close app from memory for better user experience.\n            </p>\n          </div>\n\n          {/* App Settings Section */}\n          <div className=\"settings-section\">\n            <h2>App Settings</h2>\n            \n            <div className=\"setting-item\" onClick={handleLanguageChange}>\n              <div className=\"setting-icon\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z\"/>\n                </svg>\n              </div>\n              <span className=\"setting-label\">Language</span>\n              <span className=\"setting-value\">{selectedLanguage}</span>\n            </div>\n\n            <div className=\"setting-item\" onClick={handleThemeChange}>\n              <div className=\"setting-icon\">\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9c.83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8zm-5.5 9c-.83 0-1.5-.67-1.5-1.5S5.67 9 6.5 9 8 9.67 8 10.5 7.33 12 6.5 12zm3-4C8.67 8 8 7.33 8 6.5S8.67 5 9.5 5s1.5.67 1.5 1.5S10.33 8 9.5 8zm5 0c-.83 0-1.5-.67-1.5-1.5S13.67 5 14.5 5s1.5.67 1.5 1.5S15.33 8 14.5 8zm3 4c-.83 0-1.5-.67-1.5-1.5S16.67 9 17.5 9s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z\"/>\n                </svg>\n              </div>\n              <span className=\"setting-label\">Theme</span>\n              <span className=\"setting-value\">{selectedTheme}</span>\n            </div>\n          </div>\n\n          {/* Logout Button */}\n          <div className=\"logout-section\">\n            <button className=\"logout-btn\" onClick={handleLogout}>\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z\"/>\n              </svg>\n              Logout\n            </button>\n          </div>\n        </div>\n\n        {/* Bottom Navigation */}\n        <div className=\"bottom-nav\">\n          <button \n            className=\"nav-btn\" \n            onClick={() => handleNavigation('/home')}\n          >\n            <img src={homeIcon} alt=\"Home\" />\n            <span>Home</span>\n          </button>\n          <button \n            className=\"nav-btn\"\n            onClick={() => handleNavigation('/public-eye')}\n          >\n            <img src={publicEyeIcon} alt=\"Public Eye\" />\n            <span>Public Eye</span>\n          </button>\n          <button className=\"nav-btn active\">\n            <img src={settingsIcon} alt=\"Settings\" />\n            <span>Settings</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW,IAAI;IAAEC;EAAO,CAAC,GAAGX,OAAO,CAAC,CAAC;EAClC,MAAM,CAACY,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACkB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC,SAAS,CAAC;EACnE,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,OAAO,CAAC;EAE3D,MAAMwB,gBAAgB,GAAIC,IAAI,IAAK;IACjCd,QAAQ,CAACc,IAAI,CAAC;EAChB,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACAC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;EACrC,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAF,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;EACxC,CAAC;EAED,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAH,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;EACxC,CAAC;EAED,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACAR,gBAAgB,CAACD,aAAa,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;EAChE,CAAC;EAED,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACzBnB,MAAM,CAAC,CAAC;IACRF,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,oBACEH,OAAA;IAAKyB,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACjC1B,OAAA;MAAKyB,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B1B,OAAA;QAAKyB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B1B,OAAA;UAAA0B,QAAA,EAAI;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eAEN9B,OAAA;QAAKyB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAE/B1B,OAAA;UAAKyB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC1B,OAAA;YAAKyB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC1B,OAAA;cAAK+B,GAAG,EAAEjC,IAAK;cAACkC,GAAG,EAAC,SAAS;cAACP,SAAS,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3D9B,OAAA;cAAKyB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B1B,OAAA;gBAAA0B,QAAA,EAAK,CAAAtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,IAAI,KAAI;cAAc;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvC9B,OAAA;gBAAA0B,QAAA,EAAI,CAAAtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,KAAK,KAAI;cAA0B;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN9B,OAAA;YAAQyB,SAAS,EAAC,kBAAkB;YAACU,OAAO,EAAEjB,iBAAkB;YAAAQ,QAAA,eAC9D1B,OAAA;cAAKoC,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,cAAc;cAAAb,QAAA,eACjE1B,OAAA;gBAAMwC,CAAC,EAAC;cAAuJ;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9J;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN9B,OAAA;UAAKyB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B1B,OAAA;YAAA0B,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEtB9B,OAAA;YAAKyB,SAAS,EAAC,cAAc;YAACU,OAAO,EAAEd,oBAAqB;YAAAK,QAAA,gBAC1D1B,OAAA;cAAKyB,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B1B,OAAA;gBAAKoC,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAb,QAAA,eACjE1B,OAAA;kBAAMwC,CAAC,EAAC;gBAAqO;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9B,OAAA;cAAMyB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAEN9B,OAAA;YAAKyB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B1B,OAAA;cAAKyB,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B1B,OAAA;gBAAKoC,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAb,QAAA,eACjE1B,OAAA;kBAAMwC,CAAC,EAAC;gBAAgf;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9B,OAAA;cAAMyB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtD9B,OAAA;cAAKyB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7B1B,OAAA;gBAAOyB,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC9B1B,OAAA;kBACEyC,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAEpC,gBAAiB;kBAC1BqC,QAAQ,EAAGC,CAAC,IAAKrC,mBAAmB,CAACqC,CAAC,CAACC,MAAM,CAACH,OAAO;gBAAE;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACF9B,OAAA;kBAAMyB,SAAS,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9B,OAAA;YAAKyB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B1B,OAAA;cAAKyB,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B1B,OAAA;gBAAKoC,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAb,QAAA,eACjE1B,OAAA;kBAAMwC,CAAC,EAAC;gBAAkK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9B,OAAA;cAAMyB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpD9B,OAAA;cAAKyB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7B1B,OAAA;gBAAOyB,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC9B1B,OAAA;kBACEyC,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAElC,oBAAqB;kBAC9BmC,QAAQ,EAAGC,CAAC,IAAKnC,uBAAuB,CAACmC,CAAC,CAACC,MAAM,CAACH,OAAO;gBAAE;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC,eACF9B,OAAA;kBAAMyB,SAAS,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9B,OAAA;UAAKyB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjC1B,OAAA;YAAKyB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B1B,OAAA;cAAMyB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjD9B,OAAA;cAAKyB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7B1B,OAAA;gBAAOyB,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC9B1B,OAAA;kBACEyC,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAEhC,gBAAiB;kBAC1BiC,QAAQ,EAAGC,CAAC,IAAKjC,mBAAmB,CAACiC,CAAC,CAACC,MAAM,CAACH,OAAO;gBAAE;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACF9B,OAAA;kBAAMyB,SAAS,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN9B,OAAA;YAAGyB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAG/B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGN9B,OAAA;UAAKyB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B1B,OAAA;YAAA0B,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAErB9B,OAAA;YAAKyB,SAAS,EAAC,cAAc;YAACU,OAAO,EAAEb,oBAAqB;YAAAI,QAAA,gBAC1D1B,OAAA;cAAKyB,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B1B,OAAA;gBAAKoC,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAb,QAAA,eACjE1B,OAAA;kBAAMwC,CAAC,EAAC;gBAA0S;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9B,OAAA;cAAMyB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/C9B,OAAA;cAAMyB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEd;YAAgB;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eAEN9B,OAAA;YAAKyB,SAAS,EAAC,cAAc;YAACU,OAAO,EAAEZ,iBAAkB;YAAAG,QAAA,gBACvD1B,OAAA;cAAKyB,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B1B,OAAA;gBAAKoC,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAb,QAAA,eACjE1B,OAAA;kBAAMwC,CAAC,EAAC;gBAAsc;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7c;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9B,OAAA;cAAMyB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5C9B,OAAA;cAAMyB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEZ;YAAa;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9B,OAAA;UAAKyB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7B1B,OAAA;YAAQyB,SAAS,EAAC,YAAY;YAACU,OAAO,EAAEX,YAAa;YAAAE,QAAA,gBACnD1B,OAAA;cAAKoC,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,cAAc;cAAAb,QAAA,eACjE1B,OAAA;gBAAMwC,CAAC,EAAC;cAAgH;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvH,CAAC,UAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9B,OAAA;QAAKyB,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB1B,OAAA;UACEyB,SAAS,EAAC,SAAS;UACnBU,OAAO,EAAEA,CAAA,KAAMnB,gBAAgB,CAAC,OAAO,CAAE;UAAAU,QAAA,gBAEzC1B,OAAA;YAAK+B,GAAG,EAAEpC,QAAS;YAACqC,GAAG,EAAC;UAAM;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjC9B,OAAA;YAAA0B,QAAA,EAAM;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACT9B,OAAA;UACEyB,SAAS,EAAC,SAAS;UACnBU,OAAO,EAAEA,CAAA,KAAMnB,gBAAgB,CAAC,aAAa,CAAE;UAAAU,QAAA,gBAE/C1B,OAAA;YAAK+B,GAAG,EAAEnC,aAAc;YAACoC,GAAG,EAAC;UAAY;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5C9B,OAAA;YAAA0B,QAAA,EAAM;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACT9B,OAAA;UAAQyB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAChC1B,OAAA;YAAK+B,GAAG,EAAElC,YAAa;YAACmC,GAAG,EAAC;UAAU;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzC9B,OAAA;YAAA0B,QAAA,EAAM;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5B,EAAA,CAnMID,QAAQ;EAAA,QACKR,WAAW,EACHC,OAAO;AAAA;AAAAoD,EAAA,GAF5B7C,QAAQ;AAqMd,eAAeA,QAAQ;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}