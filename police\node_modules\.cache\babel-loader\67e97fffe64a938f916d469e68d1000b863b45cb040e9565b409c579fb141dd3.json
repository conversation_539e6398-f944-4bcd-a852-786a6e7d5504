{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\POlice\\\\police\\\\src\\\\component\\\\FilterModal.js\";\nimport React from 'react';\nimport './FilterModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FilterModal = ({\n  isOpen,\n  onClose,\n  selectedCategory,\n  selectedProvince,\n  onCategoryChange,\n  onProvinceChange,\n  onApply\n}) => {\n  if (!isOpen) return null;\n  const newsCategories = ['all', 'fire', 'cyber crime', 'informative', 'harmful weapon', 'flood landslide', 'organizational program', 'gambling', 'dead bodies found', 'rape', 'home ministry program', 'Narcotic', 'IGP program', 'blackmailing', 'Quarrel/Disturbance', 'Bribery', 'drug', 'violence', 'Suspicious thing', 'crime report', 'burglary', 'pick pocketing', 'harassment', 'illegal trading', 'police day program', 'misbehaviour', 'robbery', 'public gathering', 'crime(arrest)', 'human trafficking', 'miscellaneous'];\n  const provinces = ['All Provinces', 'Province 1', 'Madhesh Province', 'Bagmati Province', 'Gandaki Province', 'Lumbini Province', 'Karnali Province', 'Sudurpashchim Province'];\n  const getCategoryDisplayName = category => {\n    if (category === 'all') return 'All Categories';\n    return category.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');\n  };\n  const handleApply = () => {\n    onApply();\n    onClose();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"filter-modal-overlay\",\n    onClick: onClose,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"filter-modal-card\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"filter-close-btn\",\n          onClick: onClose,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Select Filters\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"filter-save-btn\",\n          onClick: handleApply,\n          children: \"Apply\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-options-grid\",\n            children: newsCategories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `filter-option-btn ${selectedCategory === category ? 'active' : ''}`,\n              onClick: () => onCategoryChange(category),\n              children: getCategoryDisplayName(category)\n            }, category, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Province\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-options-grid\",\n            children: provinces.map(province => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `filter-option-btn ${selectedProvince === province ? 'active' : ''}`,\n              onClick: () => onProvinceChange(province),\n              children: province\n            }, province, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n_c = FilterModal;\nexport default FilterModal;\nvar _c;\n$RefreshReg$(_c, \"FilterModal\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "FilterModal", "isOpen", "onClose", "selectedCate<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onCategoryChange", "onProvinceChange", "onApply", "newsCategories", "provinces", "getCategoryDisplayName", "category", "split", "map", "word", "char<PERSON>t", "toUpperCase", "slice", "join", "handleApply", "className", "onClick", "children", "e", "stopPropagation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "province", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/POlice/police/src/component/FilterModal.js"], "sourcesContent": ["import React from 'react';\nimport './FilterModal.css';\n\nconst FilterModal = ({ \n  isOpen, \n  onClose, \n  selectedCategory, \n  selectedProvince, \n  onCategoryChange, \n  onProvinceChange,\n  onApply \n}) => {\n  if (!isOpen) return null;\n\n  const newsCategories = [\n    'all',\n    'fire',\n    'cyber crime',\n    'informative',\n    'harmful weapon',\n    'flood landslide',\n    'organizational program',\n    'gambling',\n    'dead bodies found',\n    'rape',\n    'home ministry program',\n    'Narcotic',\n    'IGP program',\n    'blackmailing',\n    'Quarrel/Disturbance',\n    'Bribery',\n    'drug',\n    'violence',\n    'Suspicious thing',\n    'crime report',\n    'burglary',\n    'pick pocketing',\n    'harassment',\n    'illegal trading',\n    'police day program',\n    'misbehaviour',\n    'robbery',\n    'public gathering',\n    'crime(arrest)',\n    'human trafficking',\n    'miscellaneous'\n  ];\n\n  const provinces = [\n    'All Provinces',\n    'Province 1',\n    'Madhesh Province',\n    'Bagmati Province',\n    'Gandaki Province',\n    'Lumbini Province',\n    'Karnali Province',\n    'Sudurpashchim Province'\n  ];\n\n  const getCategoryDisplayName = (category) => {\n    if (category === 'all') return 'All Categories';\n    return category.split(' ').map(word => \n      word.charAt(0).toUpperCase() + word.slice(1)\n    ).join(' ');\n  };\n\n  const handleApply = () => {\n    onApply();\n    onClose();\n  };\n\n  return (\n    <div className=\"filter-modal-overlay\" onClick={onClose}>\n      <div className=\"filter-modal-card\" onClick={(e) => e.stopPropagation()}>\n        <div className=\"filter-modal-header\">\n          <button className=\"filter-close-btn\" onClick={onClose}>×</button>\n          <h2>Select Filters</h2>\n          <button className=\"filter-save-btn\" onClick={handleApply}>Apply</button>\n        </div>\n\n        <div className=\"filter-modal-content\">\n          <div className=\"filter-section\">\n            <h3>Category</h3>\n            <div className=\"filter-options-grid\">\n              {newsCategories.map(category => (\n                <button\n                  key={category}\n                  className={`filter-option-btn ${selectedCategory === category ? 'active' : ''}`}\n                  onClick={() => onCategoryChange(category)}\n                >\n                  {getCategoryDisplayName(category)}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          <div className=\"filter-section\">\n            <h3>Province</h3>\n            <div className=\"filter-options-grid\">\n              {provinces.map(province => (\n                <button\n                  key={province}\n                  className={`filter-option-btn ${selectedProvince === province ? 'active' : ''}`}\n                  onClick={() => onProvinceChange(province)}\n                >\n                  {province}\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FilterModal;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EACnBC,MAAM;EACNC,OAAO;EACPC,gBAAgB;EAChBC,gBAAgB;EAChBC,gBAAgB;EAChBC,gBAAgB;EAChBC;AACF,CAAC,KAAK;EACJ,IAAI,CAACN,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMO,cAAc,GAAG,CACrB,KAAK,EACL,MAAM,EACN,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,iBAAiB,EACjB,wBAAwB,EACxB,UAAU,EACV,mBAAmB,EACnB,MAAM,EACN,uBAAuB,EACvB,UAAU,EACV,aAAa,EACb,cAAc,EACd,qBAAqB,EACrB,SAAS,EACT,MAAM,EACN,UAAU,EACV,kBAAkB,EAClB,cAAc,EACd,UAAU,EACV,gBAAgB,EAChB,YAAY,EACZ,iBAAiB,EACjB,oBAAoB,EACpB,cAAc,EACd,SAAS,EACT,kBAAkB,EAClB,eAAe,EACf,mBAAmB,EACnB,eAAe,CAChB;EAED,MAAMC,SAAS,GAAG,CAChB,eAAe,EACf,YAAY,EACZ,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,wBAAwB,CACzB;EAED,MAAMC,sBAAsB,GAAIC,QAAQ,IAAK;IAC3C,IAAIA,QAAQ,KAAK,KAAK,EAAE,OAAO,gBAAgB;IAC/C,OAAOA,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,IAAI,IACjCA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAC7C,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACb,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBZ,OAAO,CAAC,CAAC;IACTL,OAAO,CAAC,CAAC;EACX,CAAC;EAED,oBACEH,OAAA;IAAKqB,SAAS,EAAC,sBAAsB;IAACC,OAAO,EAAEnB,OAAQ;IAAAoB,QAAA,eACrDvB,OAAA;MAAKqB,SAAS,EAAC,mBAAmB;MAACC,OAAO,EAAGE,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;MAAAF,QAAA,gBACrEvB,OAAA;QAAKqB,SAAS,EAAC,qBAAqB;QAAAE,QAAA,gBAClCvB,OAAA;UAAQqB,SAAS,EAAC,kBAAkB;UAACC,OAAO,EAAEnB,OAAQ;UAAAoB,QAAA,EAAC;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACjE7B,OAAA;UAAAuB,QAAA,EAAI;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvB7B,OAAA;UAAQqB,SAAS,EAAC,iBAAiB;UAACC,OAAO,EAAEF,WAAY;UAAAG,QAAA,EAAC;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,eAEN7B,OAAA;QAAKqB,SAAS,EAAC,sBAAsB;QAAAE,QAAA,gBACnCvB,OAAA;UAAKqB,SAAS,EAAC,gBAAgB;UAAAE,QAAA,gBAC7BvB,OAAA;YAAAuB,QAAA,EAAI;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjB7B,OAAA;YAAKqB,SAAS,EAAC,qBAAqB;YAAAE,QAAA,EACjCd,cAAc,CAACK,GAAG,CAACF,QAAQ,iBAC1BZ,OAAA;cAEEqB,SAAS,EAAE,qBAAqBjB,gBAAgB,KAAKQ,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;cAChFU,OAAO,EAAEA,CAAA,KAAMhB,gBAAgB,CAACM,QAAQ,CAAE;cAAAW,QAAA,EAEzCZ,sBAAsB,CAACC,QAAQ;YAAC,GAJ5BA,QAAQ;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKP,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7B,OAAA;UAAKqB,SAAS,EAAC,gBAAgB;UAAAE,QAAA,gBAC7BvB,OAAA;YAAAuB,QAAA,EAAI;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjB7B,OAAA;YAAKqB,SAAS,EAAC,qBAAqB;YAAAE,QAAA,EACjCb,SAAS,CAACI,GAAG,CAACgB,QAAQ,iBACrB9B,OAAA;cAEEqB,SAAS,EAAE,qBAAqBhB,gBAAgB,KAAKyB,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;cAChFR,OAAO,EAAEA,CAAA,KAAMf,gBAAgB,CAACuB,QAAQ,CAAE;cAAAP,QAAA,EAEzCO;YAAQ,GAJJA,QAAQ;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKP,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GA/GI9B,WAAW;AAiHjB,eAAeA,WAAW;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}