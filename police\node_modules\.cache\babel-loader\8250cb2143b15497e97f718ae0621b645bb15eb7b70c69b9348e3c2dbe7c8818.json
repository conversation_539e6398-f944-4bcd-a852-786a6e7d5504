{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\POlice\\\\police\\\\src\\\\component\\\\NewsDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport './News.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NewsDetail = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    id\n  } = useParams();\n  const [news, setNews] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [liked, setLiked] = useState(false);\n  const fetchNewsDetail = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`http://localhost:5000/api/news/${id}`, {\n        withCredentials: true\n      });\n      setNews(response.data);\n    } catch (err) {\n      setError('Failed to fetch news details');\n      console.error('Error fetching news detail:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, [id]);\n  useEffect(() => {\n    if (id) {\n      fetchNewsDetail();\n    }\n  }, [id, fetchNewsDetail]);\n  const handleBack = () => {\n    navigate('/news');\n  };\n  const handleLike = async () => {\n    try {\n      const response = await axios.post(`http://localhost:5000/api/news/${id}/like`, {}, {\n        withCredentials: true\n      });\n      setNews(prev => ({\n        ...prev,\n        likes: response.data.likes\n      }));\n      setLiked(true);\n    } catch (err) {\n      console.error('Error liking news:', err);\n    }\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n\n    // Simple B.S. conversion (approximate)\n    const bsYear = year + 57;\n    return `${year}-${month}-${day} (${bsYear}-${month}-${day} B.S)`;\n  };\n  const getCategoryDisplayName = category => {\n    return category.charAt(0).toUpperCase() + category.slice(1);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"news-detail-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"news-detail-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"news-detail-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"news-back-btn\",\n            onClick: handleBack,\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\",\n                fill: \"white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"News Detail\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '40px',\n              height: '40px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"news-detail-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"news-loading\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading news...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !news) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"news-detail-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"news-detail-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"news-detail-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"news-back-btn\",\n            onClick: handleBack,\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\",\n                fill: \"white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"News Detail\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '40px',\n              height: '40px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"news-detail-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"news-error\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: error || 'News not found'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: fetchNewsDetail,\n              className: \"retry-btn\",\n              children: \"Retry\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"news-detail-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"news-detail-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"news-detail-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"news-back-btn\",\n          onClick: handleBack,\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"24\",\n            height: \"24\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\",\n              fill: \"white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"News Detail\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '40px',\n            height: '40px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"news-detail-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"news-detail-meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"news-detail-date\",\n            children: formatDate(news.createdAt)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"news-detail-category\",\n            children: getCategoryDisplayName(news.category)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"news-detail-title\",\n          children: news.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), news.image && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"news-detail-image\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `http://localhost:5000${news.image.url}`,\n            alt: news.title,\n            onError: e => {\n              e.target.parentElement.style.display = 'none';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"news-detail-text\",\n          children: news.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"news-detail-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `like-btn ${liked ? 'liked' : ''}`,\n            onClick: handleLike,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              fill: liked ? 'currentColor' : 'none',\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), news.likes || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"news-stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), news.views || 0, \" views\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), news.createdBy && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"news-stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"7\",\n                r: \"4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), \"By \", news.createdBy.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this), news.province && news.province !== 'All Provinces' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"news-stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"10\",\n                r: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this), news.province]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 5\n  }, this);\n};\n_s(NewsDetail, \"zoYTLiMQKFaZqJduBhdaV2sPmKg=\", false, function () {\n  return [useNavigate, useParams];\n});\n_c = NewsDetail;\nexport default NewsDetail;\nvar _c;\n$RefreshReg$(_c, \"NewsDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useNavigate", "useParams", "axios", "jsxDEV", "_jsxDEV", "NewsDetail", "_s", "navigate", "id", "news", "setNews", "loading", "setLoading", "error", "setError", "liked", "setLiked", "fetchNewsDetail", "response", "get", "withCredentials", "data", "err", "console", "handleBack", "handleLike", "post", "prev", "likes", "formatDate", "dateString", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "bsYear", "getCategoryDisplayName", "category", "char<PERSON>t", "toUpperCase", "slice", "className", "children", "onClick", "width", "height", "viewBox", "d", "fill", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "createdAt", "title", "image", "src", "url", "alt", "onError", "e", "target", "parentElement", "display", "content", "stroke", "cx", "cy", "r", "views", "created<PERSON>y", "name", "province", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/POlice/police/src/component/NewsDetail.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport './News.css';\n\nconst NewsDetail = () => {\n  const navigate = useNavigate();\n  const { id } = useParams();\n  const [news, setNews] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [liked, setLiked] = useState(false);\n\n  const fetchNewsDetail = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`http://localhost:5000/api/news/${id}`, {\n        withCredentials: true\n      });\n      setNews(response.data);\n    } catch (err) {\n      setError('Failed to fetch news details');\n      console.error('Error fetching news detail:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, [id]);\n\n  useEffect(() => {\n    if (id) {\n      fetchNewsDetail();\n    }\n  }, [id, fetchNewsDetail]);\n\n  const handleBack = () => {\n    navigate('/news');\n  };\n\n  const handleLike = async () => {\n    try {\n      const response = await axios.post(`http://localhost:5000/api/news/${id}/like`, {}, {\n        withCredentials: true\n      });\n      setNews(prev => ({\n        ...prev,\n        likes: response.data.likes\n      }));\n      setLiked(true);\n    } catch (err) {\n      console.error('Error liking news:', err);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    \n    // Simple B.S. conversion (approximate)\n    const bsYear = year + 57;\n    return `${year}-${month}-${day} (${bsYear}-${month}-${day} B.S)`;\n  };\n\n  const getCategoryDisplayName = (category) => {\n    return category.charAt(0).toUpperCase() + category.slice(1);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"news-detail-container\">\n        <div className=\"news-detail-card\">\n          <div className=\"news-detail-header\">\n            <button className=\"news-back-btn\" onClick={handleBack}>\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\">\n                <path d=\"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\" fill=\"white\"/>\n              </svg>\n            </button>\n            <h1>News Detail</h1>\n            <div style={{ width: '40px', height: '40px' }}></div>\n          </div>\n          <div className=\"news-detail-content\">\n            <div className=\"news-loading\">\n              <div className=\"loading-spinner\"></div>\n              <p>Loading news...</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !news) {\n    return (\n      <div className=\"news-detail-container\">\n        <div className=\"news-detail-card\">\n          <div className=\"news-detail-header\">\n            <button className=\"news-back-btn\" onClick={handleBack}>\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\">\n                <path d=\"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\" fill=\"white\"/>\n              </svg>\n            </button>\n            <h1>News Detail</h1>\n            <div style={{ width: '40px', height: '40px' }}></div>\n          </div>\n          <div className=\"news-detail-content\">\n            <div className=\"news-error\">\n              <p>{error || 'News not found'}</p>\n              <button onClick={fetchNewsDetail} className=\"retry-btn\">Retry</button>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"news-detail-container\">\n      <div className=\"news-detail-card\">\n        <div className=\"news-detail-header\">\n          <button className=\"news-back-btn\" onClick={handleBack}>\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\">\n              <path d=\"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\" fill=\"white\"/>\n            </svg>\n          </button>\n          <h1>News Detail</h1>\n          <div style={{ width: '40px', height: '40px' }}></div>\n        </div>\n\n        <div className=\"news-detail-content\">\n          <div className=\"news-detail-meta\">\n            <div className=\"news-detail-date\">\n              {formatDate(news.createdAt)}\n            </div>\n            <div className=\"news-detail-category\">\n              {getCategoryDisplayName(news.category)}\n            </div>\n          </div>\n\n          <h2 className=\"news-detail-title\">{news.title}</h2>\n\n          {news.image && (\n            <div className=\"news-detail-image\">\n              <img \n                src={`http://localhost:5000${news.image.url}`} \n                alt={news.title}\n                onError={(e) => {\n                  e.target.parentElement.style.display = 'none';\n                }}\n              />\n            </div>\n          )}\n\n          <div className=\"news-detail-text\">\n            {news.content}\n          </div>\n\n          <div className=\"news-detail-stats\">\n            <button \n              className={`like-btn ${liked ? 'liked' : ''}`}\n              onClick={handleLike}\n            >\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill={liked ? 'currentColor' : 'none'} stroke=\"currentColor\">\n                <path d=\"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"/>\n              </svg>\n              {news.likes || 0}\n            </button>\n\n            <div className=\"news-stat\">\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                <path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"/>\n                <circle cx=\"12\" cy=\"12\" r=\"3\"/>\n              </svg>\n              {news.views || 0} views\n            </div>\n\n            {news.createdBy && (\n              <div className=\"news-stat\">\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                  <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"/>\n                  <circle cx=\"12\" cy=\"7\" r=\"4\"/>\n                </svg>\n                By {news.createdBy.name}\n              </div>\n            )}\n\n            {news.province && news.province !== 'All Provinces' && (\n              <div className=\"news-stat\">\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                  <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"/>\n                  <circle cx=\"12\" cy=\"10\" r=\"3\"/>\n                </svg>\n                {news.province}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default NewsDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEQ;EAAG,CAAC,GAAGP,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACQ,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAEzC,MAAMoB,eAAe,GAAGlB,WAAW,CAAC,YAAY;IAC9C,IAAI;MACFa,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAMhB,KAAK,CAACiB,GAAG,CAAC,kCAAkCX,EAAE,EAAE,EAAE;QACvEY,eAAe,EAAE;MACnB,CAAC,CAAC;MACFV,OAAO,CAACQ,QAAQ,CAACG,IAAI,CAAC;IACxB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZR,QAAQ,CAAC,8BAA8B,CAAC;MACxCS,OAAO,CAACV,KAAK,CAAC,6BAA6B,EAAES,GAAG,CAAC;IACnD,CAAC,SAAS;MACRV,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACJ,EAAE,CAAC,CAAC;EAERV,SAAS,CAAC,MAAM;IACd,IAAIU,EAAE,EAAE;MACNS,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACT,EAAE,EAAES,eAAe,CAAC,CAAC;EAEzB,MAAMO,UAAU,GAAGA,CAAA,KAAM;IACvBjB,QAAQ,CAAC,OAAO,CAAC;EACnB,CAAC;EAED,MAAMkB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMhB,KAAK,CAACwB,IAAI,CAAC,kCAAkClB,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE;QACjFY,eAAe,EAAE;MACnB,CAAC,CAAC;MACFV,OAAO,CAACiB,IAAI,KAAK;QACf,GAAGA,IAAI;QACPC,KAAK,EAAEV,QAAQ,CAACG,IAAI,CAACO;MACvB,CAAC,CAAC,CAAC;MACHZ,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOM,GAAG,EAAE;MACZC,OAAO,CAACV,KAAK,CAAC,oBAAoB,EAAES,GAAG,CAAC;IAC1C;EACF,CAAC;EAED,MAAMO,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,IAAI,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;;IAEnD;IACA,MAAMG,MAAM,GAAGR,IAAI,GAAG,EAAE;IACxB,OAAO,GAAGA,IAAI,IAAIE,KAAK,IAAII,GAAG,KAAKE,MAAM,IAAIN,KAAK,IAAII,GAAG,OAAO;EAClE,CAAC;EAED,MAAMG,sBAAsB,GAAIC,QAAQ,IAAK;IAC3C,OAAOA,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,QAAQ,CAACG,KAAK,CAAC,CAAC,CAAC;EAC7D,CAAC;EAED,IAAInC,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK2C,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpC5C,OAAA;QAAK2C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B5C,OAAA;UAAK2C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjC5C,OAAA;YAAQ2C,SAAS,EAAC,eAAe;YAACE,OAAO,EAAEzB,UAAW;YAAAwB,QAAA,eACpD5C,OAAA;cAAK8C,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAC7C5C,OAAA;gBAAMiD,CAAC,EAAC,8DAA8D;gBAACC,IAAI,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACTtD,OAAA;YAAA4C,QAAA,EAAI;UAAW;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBtD,OAAA;YAAKuD,KAAK,EAAE;cAAET,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNtD,OAAA;UAAK2C,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClC5C,OAAA;YAAK2C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B5C,OAAA;cAAK2C,SAAS,EAAC;YAAiB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvCtD,OAAA;cAAA4C,QAAA,EAAG;YAAe;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI7C,KAAK,IAAI,CAACJ,IAAI,EAAE;IAClB,oBACEL,OAAA;MAAK2C,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpC5C,OAAA;QAAK2C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B5C,OAAA;UAAK2C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjC5C,OAAA;YAAQ2C,SAAS,EAAC,eAAe;YAACE,OAAO,EAAEzB,UAAW;YAAAwB,QAAA,eACpD5C,OAAA;cAAK8C,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAC7C5C,OAAA;gBAAMiD,CAAC,EAAC,8DAA8D;gBAACC,IAAI,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACTtD,OAAA;YAAA4C,QAAA,EAAI;UAAW;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBtD,OAAA;YAAKuD,KAAK,EAAE;cAAET,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNtD,OAAA;UAAK2C,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClC5C,OAAA;YAAK2C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5C,OAAA;cAAA4C,QAAA,EAAInC,KAAK,IAAI;YAAgB;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClCtD,OAAA;cAAQ6C,OAAO,EAAEhC,eAAgB;cAAC8B,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEtD,OAAA;IAAK2C,SAAS,EAAC,uBAAuB;IAAAC,QAAA,eACpC5C,OAAA;MAAK2C,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B5C,OAAA;QAAK2C,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC5C,OAAA;UAAQ2C,SAAS,EAAC,eAAe;UAACE,OAAO,EAAEzB,UAAW;UAAAwB,QAAA,eACpD5C,OAAA;YAAK8C,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC7C5C,OAAA;cAAMiD,CAAC,EAAC,8DAA8D;cAACC,IAAI,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACTtD,OAAA;UAAA4C,QAAA,EAAI;QAAW;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpBtD,OAAA;UAAKuD,KAAK,EAAE;YAAET,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAO;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eAENtD,OAAA;QAAK2C,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClC5C,OAAA;UAAK2C,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B5C,OAAA;YAAK2C,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9BnB,UAAU,CAACpB,IAAI,CAACmD,SAAS;UAAC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACNtD,OAAA;YAAK2C,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAClCN,sBAAsB,CAACjC,IAAI,CAACkC,QAAQ;UAAC;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtD,OAAA;UAAI2C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAEvC,IAAI,CAACoD;QAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAElDjD,IAAI,CAACqD,KAAK,iBACT1D,OAAA;UAAK2C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChC5C,OAAA;YACE2D,GAAG,EAAE,wBAAwBtD,IAAI,CAACqD,KAAK,CAACE,GAAG,EAAG;YAC9CC,GAAG,EAAExD,IAAI,CAACoD,KAAM;YAChBK,OAAO,EAAGC,CAAC,IAAK;cACdA,CAAC,CAACC,MAAM,CAACC,aAAa,CAACV,KAAK,CAACW,OAAO,GAAG,MAAM;YAC/C;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAEDtD,OAAA;UAAK2C,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC9BvC,IAAI,CAAC8D;QAAO;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENtD,OAAA;UAAK2C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5C,OAAA;YACE2C,SAAS,EAAE,YAAYhC,KAAK,GAAG,OAAO,GAAG,EAAE,EAAG;YAC9CkC,OAAO,EAAExB,UAAW;YAAAuB,QAAA,gBAEpB5C,OAAA;cAAK8C,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACE,IAAI,EAAEvC,KAAK,GAAG,cAAc,GAAG,MAAO;cAACyD,MAAM,EAAC,cAAc;cAAAxB,QAAA,eAC1G5C,OAAA;gBAAMiD,CAAC,EAAC;cAA0I;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjJ,CAAC,EACLjD,IAAI,CAACmB,KAAK,IAAI,CAAC;UAAA;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAETtD,OAAA;YAAK2C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5C,OAAA;cAAK8C,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACE,IAAI,EAAC,MAAM;cAACkB,MAAM,EAAC,cAAc;cAAAxB,QAAA,gBAC/E5C,OAAA;gBAAMiD,CAAC,EAAC;cAA8C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eACxDtD,OAAA;gBAAQqE,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,EACLjD,IAAI,CAACmE,KAAK,IAAI,CAAC,EAAC,QACnB;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EAELjD,IAAI,CAACoE,SAAS,iBACbzE,OAAA;YAAK2C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5C,OAAA;cAAK8C,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACE,IAAI,EAAC,MAAM;cAACkB,MAAM,EAAC,cAAc;cAAAxB,QAAA,gBAC/E5C,OAAA;gBAAMiD,CAAC,EAAC;cAA2C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eACrDtD,OAAA;gBAAQqE,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,CAAC,EAAC;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,OACH,EAACjD,IAAI,CAACoE,SAAS,CAACC,IAAI;UAAA;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CACN,EAEAjD,IAAI,CAACsE,QAAQ,IAAItE,IAAI,CAACsE,QAAQ,KAAK,eAAe,iBACjD3E,OAAA;YAAK2C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5C,OAAA;cAAK8C,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACE,IAAI,EAAC,MAAM;cAACkB,MAAM,EAAC,cAAc;cAAAxB,QAAA,gBAC/E5C,OAAA;gBAAMiD,CAAC,EAAC;cAAgD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAC1DtD,OAAA;gBAAQqE,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,EACLjD,IAAI,CAACsE,QAAQ;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpD,EAAA,CAnMID,UAAU;EAAA,QACGL,WAAW,EACbC,SAAS;AAAA;AAAA+E,EAAA,GAFpB3E,UAAU;AAqMhB,eAAeA,UAAU;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}