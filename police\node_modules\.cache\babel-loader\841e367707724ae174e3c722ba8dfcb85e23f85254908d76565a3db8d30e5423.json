{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\POlice\\\\police\\\\src\\\\component\\\\News.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport axios from 'axios';\nimport FilterModal from './FilterModal';\nimport './News.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst News = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useAuth();\n  const [news, setNews] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedProvince, setSelectedProvince] = useState('All Provinces');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const newsCategories = ['all', 'fire', 'cyber crime', 'informative', 'harmful weapon', 'flood landslide', 'organizational program', 'gambling', 'dead bodies found', 'rape', 'home ministry program', 'narcotic', 'igp program', 'blackmailing', 'quarrel/disturbance', 'bribery', 'drug', 'violence', 'suspicious thing', 'crime report', 'burglary', 'pick pocketing', 'harassment', 'illegal trading', 'police day program', 'misbehaviour', 'robbery', 'public gathering', 'crime(arrest)', 'human trafficking', 'miscellaneous'];\n  const provinces = ['All Provinces', 'Province 1', 'Madhesh Province', 'Bagmati Province', 'Gandaki Province', 'Lumbini Province', 'Karnali Province', 'Sudurpashchim Province'];\n  useEffect(() => {\n    fetchNews();\n  }, [selectedCategory, selectedProvince, searchQuery]);\n  const fetchNews = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      if (selectedCategory !== 'all') {\n        params.append('category', selectedCategory);\n      }\n      if (selectedProvince !== 'All Provinces') {\n        params.append('province', selectedProvince);\n      }\n      if (searchQuery.trim()) {\n        params.append('search', searchQuery.trim());\n      }\n      const response = await axios.get(`http://localhost:5000/api/news?${params.toString()}`, {\n        withCredentials: true\n      });\n      setNews(response.data.news || []);\n    } catch (err) {\n      setError('Failed to fetch news');\n      console.error('Error fetching news:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleBack = () => {\n    navigate('/home');\n  };\n  const handleCategorySelect = category => {\n    setSelectedCategory(category);\n  };\n  const handleProvinceChange = e => {\n    setSelectedProvince(e.target.value);\n  };\n  const handleSearchChange = e => {\n    setSearchQuery(e.target.value);\n  };\n  const handleNewsClick = newsId => {\n    navigate(`/news/${newsId}`);\n  };\n  const handleAdminPanel = () => {\n    navigate('/admin/news');\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n\n    // Simple B.S. conversion (approximate)\n    const bsYear = year + 57;\n    return `${year}-${month}-${day} (${bsYear}-${month}-${day} B.S)`;\n  };\n  const getCategoryDisplayName = category => {\n    return category.charAt(0).toUpperCase() + category.slice(1);\n  };\n\n  // Show all categories\n  const visibleCategories = newsCategories;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"news-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"news-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"news-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"news-back-btn\",\n          onClick: handleBack,\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"24\",\n            height: \"24\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\",\n              fill: \"white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"News\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"news-header-actions\",\n          children: [user && user.role === 'admin' && /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn\",\n            onClick: handleAdminPanel,\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"white\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"filter-btn\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"white\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M4.25 5.61C6.27 8.2 10 13 10 13v6c0 .55.45 1 1 1h2c.55 0 1-.45 1-1v-6s3.73-4.8 5.75-7.39C20.25 4.95 19.78 4 18.95 4H5.04c-.83 0-1.3.95-.79 1.61z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"news-search-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"news-search-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search\",\n            value: searchQuery,\n            onChange: handleSearchChange,\n            className: \"news-search-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"news-search-icon\",\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"news-province-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedProvince,\n          onChange: handleProvinceChange,\n          className: \"news-province-select\",\n          children: provinces.map(province => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: province,\n            children: province\n          }, province, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"province-dropdown-icon\",\n          width: \"20\",\n          height: \"20\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M7 10l5 5 5-5z\",\n            fill: \"currentColor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"news-categories-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"news-categories-scroll\",\n          children: visibleCategories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `category-btn ${selectedCategory === category ? 'active' : ''}`,\n            onClick: () => handleCategorySelect(category),\n            children: getCategoryDisplayName(category)\n          }, category, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"news-content\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"news-loading\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading news...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"news-error\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: fetchNews,\n            className: \"retry-btn\",\n            children: \"Retry\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this) : news.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"news-empty\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No news found for the selected criteria.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"news-list\",\n          children: news.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"news-item\",\n            onClick: () => handleNewsClick(item._id),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"news-item-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"news-item-date\",\n                children: formatDate(item.createdAt)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"news-item-title\",\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 21\n              }, this), item.content && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"news-item-preview\",\n                children: [item.content.substring(0, 100), \"...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"news-item-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"news-category\",\n                  children: getCategoryDisplayName(item.category)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 23\n                }, this), item.province !== 'All Provinces' && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"news-province\",\n                  children: item.province\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 19\n            }, this), item.image && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"news-item-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: `http://localhost:5000${item.image.url}`,\n                alt: item.title,\n                onError: e => {\n                  e.target.style.display = 'none';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 21\n            }, this)]\n          }, item._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n};\n_s(News, \"3nwFbChZt7CZdUQXVe1u2pfW3zk=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = News;\nexport default News;\nvar _c;\n$RefreshReg$(_c, \"News\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useAuth", "axios", "FilterModal", "jsxDEV", "_jsxDEV", "News", "_s", "navigate", "user", "news", "setNews", "categories", "setCategories", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedProvince", "searchQuery", "setSearch<PERSON>uery", "loading", "setLoading", "error", "setError", "newsCategories", "provinces", "fetchNews", "params", "URLSearchParams", "append", "trim", "response", "get", "toString", "withCredentials", "data", "err", "console", "handleBack", "handleCategorySelect", "category", "handleProvinceChange", "e", "target", "value", "handleSearchChange", "handleNewsClick", "newsId", "handleAdminPanel", "formatDate", "dateString", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "bsYear", "getCategoryDisplayName", "char<PERSON>t", "toUpperCase", "slice", "visibleCategories", "className", "children", "onClick", "width", "height", "viewBox", "d", "fill", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "type", "placeholder", "onChange", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "map", "province", "length", "item", "_id", "createdAt", "title", "content", "substring", "image", "src", "url", "alt", "onError", "style", "display", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/POlice/police/src/component/News.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport axios from 'axios';\nimport FilterModal from './FilterModal';\nimport './News.css';\n\nconst News = () => {\n  const navigate = useNavigate();\n  const { user } = useAuth();\n  const [news, setNews] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedProvince, setSelectedProvince] = useState('All Provinces');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  const newsCategories = [\n    'all',\n    'fire',\n    'cyber crime',\n    'informative',\n    'harmful weapon',\n    'flood landslide',\n    'organizational program',\n    'gambling',\n    'dead bodies found',\n    'rape',\n    'home ministry program',\n    'narcotic',\n    'igp program',\n    'blackmailing',\n    'quarrel/disturbance',\n    'bribery',\n    'drug',\n    'violence',\n    'suspicious thing',\n    'crime report',\n    'burglary',\n    'pick pocketing',\n    'harassment',\n    'illegal trading',\n    'police day program',\n    'misbehaviour',\n    'robbery',\n    'public gathering',\n    'crime(arrest)',\n    'human trafficking',\n    'miscellaneous'\n  ];\n\n  const provinces = [\n    'All Provinces',\n    'Province 1',\n    'Madhesh Province',\n    'Bagmati Province',\n    'Gandaki Province',\n    'Lumbini Province',\n    'Karnali Province',\n    'Sudurpashchim Province'\n  ];\n\n  useEffect(() => {\n    fetchNews();\n  }, [selectedCategory, selectedProvince, searchQuery]);\n\n  const fetchNews = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      \n      if (selectedCategory !== 'all') {\n        params.append('category', selectedCategory);\n      }\n      \n      if (selectedProvince !== 'All Provinces') {\n        params.append('province', selectedProvince);\n      }\n      \n      if (searchQuery.trim()) {\n        params.append('search', searchQuery.trim());\n      }\n\n      const response = await axios.get(`http://localhost:5000/api/news?${params.toString()}`, {\n        withCredentials: true\n      });\n      setNews(response.data.news || []);\n    } catch (err) {\n      setError('Failed to fetch news');\n      console.error('Error fetching news:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleBack = () => {\n    navigate('/home');\n  };\n\n  const handleCategorySelect = (category) => {\n    setSelectedCategory(category);\n  };\n\n  const handleProvinceChange = (e) => {\n    setSelectedProvince(e.target.value);\n  };\n\n  const handleSearchChange = (e) => {\n    setSearchQuery(e.target.value);\n  };\n\n  const handleNewsClick = (newsId) => {\n    navigate(`/news/${newsId}`);\n  };\n\n  const handleAdminPanel = () => {\n    navigate('/admin/news');\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    \n    // Simple B.S. conversion (approximate)\n    const bsYear = year + 57;\n    return `${year}-${month}-${day} (${bsYear}-${month}-${day} B.S)`;\n  };\n\n  const getCategoryDisplayName = (category) => {\n    return category.charAt(0).toUpperCase() + category.slice(1);\n  };\n\n  // Show all categories\n  const visibleCategories = newsCategories;\n\n  return (\n    <div className=\"news-container\">\n      <div className=\"news-card\">\n        <div className=\"news-header\">\n          <button className=\"news-back-btn\" onClick={handleBack}>\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\">\n              <path d=\"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\" fill=\"white\"/>\n            </svg>\n          </button>\n          <h1>News</h1>\n          <div className=\"news-header-actions\">\n            {user && user.role === 'admin' && (\n              <button className=\"admin-btn\" onClick={handleAdminPanel}>\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"white\">\n                  <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n                </svg>\n              </button>\n            )}\n            <button className=\"filter-btn\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"white\">\n                <path d=\"M4.25 5.61C6.27 8.2 10 13 10 13v6c0 .55.45 1 1 1h2c.55 0 1-.45 1-1v-6s3.73-4.8 5.75-7.39C20.25 4.95 19.78 4 18.95 4H5.04c-.83 0-1.3.95-.79 1.61z\"/>\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        <div className=\"news-search-container\">\n          <div className=\"news-search-wrapper\">\n            <input\n              type=\"text\"\n              placeholder=\"Search\"\n              value={searchQuery}\n              onChange={handleSearchChange}\n              className=\"news-search-input\"\n            />\n            <svg className=\"news-search-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n              <path d=\"M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n          </div>\n        </div>\n\n        <div className=\"news-province-container\">\n          <select \n            value={selectedProvince} \n            onChange={handleProvinceChange}\n            className=\"news-province-select\"\n          >\n            {provinces.map(province => (\n              <option key={province} value={province}>\n                {province}\n              </option>\n            ))}\n          </select>\n          <svg className=\"province-dropdown-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n            <path d=\"M7 10l5 5 5-5z\" fill=\"currentColor\"/>\n          </svg>\n        </div>\n\n        <div className=\"news-categories-container\">\n          <div className=\"news-categories-scroll\">\n            {visibleCategories.map(category => (\n              <button\n                key={category}\n                className={`category-btn ${selectedCategory === category ? 'active' : ''}`}\n                onClick={() => handleCategorySelect(category)}\n              >\n                {getCategoryDisplayName(category)}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"news-content\">\n          {loading ? (\n            <div className=\"news-loading\">\n              <div className=\"loading-spinner\"></div>\n              <p>Loading news...</p>\n            </div>\n          ) : error ? (\n            <div className=\"news-error\">\n              <p>{error}</p>\n              <button onClick={fetchNews} className=\"retry-btn\">Retry</button>\n            </div>\n          ) : news.length === 0 ? (\n            <div className=\"news-empty\">\n              <p>No news found for the selected criteria.</p>\n            </div>\n          ) : (\n            <div className=\"news-list\">\n              {news.map((item) => (\n                <div \n                  key={item._id} \n                  className=\"news-item\"\n                  onClick={() => handleNewsClick(item._id)}\n                >\n                  <div className=\"news-item-content\">\n                    <div className=\"news-item-date\">\n                      {formatDate(item.createdAt)}\n                    </div>\n                    <div className=\"news-item-title\">\n                      {item.title}\n                    </div>\n                    {item.content && (\n                      <div className=\"news-item-preview\">\n                        {item.content.substring(0, 100)}...\n                      </div>\n                    )}\n                    <div className=\"news-item-meta\">\n                      <span className=\"news-category\">{getCategoryDisplayName(item.category)}</span>\n                      {item.province !== 'All Provinces' && (\n                        <span className=\"news-province\">{item.province}</span>\n                      )}\n                    </div>\n                  </div>\n                  {item.image && (\n                    <div className=\"news-item-image\">\n                      <img \n                        src={`http://localhost:5000${item.image.url}`} \n                        alt={item.title}\n                        onError={(e) => {\n                          e.target.style.display = 'none';\n                        }}\n                      />\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default News;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACS,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnB,QAAQ,CAAC,eAAe,CAAC;EACzE,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM0B,cAAc,GAAG,CACrB,KAAK,EACL,MAAM,EACN,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,iBAAiB,EACjB,wBAAwB,EACxB,UAAU,EACV,mBAAmB,EACnB,MAAM,EACN,uBAAuB,EACvB,UAAU,EACV,aAAa,EACb,cAAc,EACd,qBAAqB,EACrB,SAAS,EACT,MAAM,EACN,UAAU,EACV,kBAAkB,EAClB,cAAc,EACd,UAAU,EACV,gBAAgB,EAChB,YAAY,EACZ,iBAAiB,EACjB,oBAAoB,EACpB,cAAc,EACd,SAAS,EACT,kBAAkB,EAClB,eAAe,EACf,mBAAmB,EACnB,eAAe,CAChB;EAED,MAAMC,SAAS,GAAG,CAChB,eAAe,EACf,YAAY,EACZ,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,wBAAwB,CACzB;EAED1B,SAAS,CAAC,MAAM;IACd2B,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACZ,gBAAgB,EAAEE,gBAAgB,EAAEE,WAAW,CAAC,CAAC;EAErD,MAAMQ,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MAEpC,IAAId,gBAAgB,KAAK,KAAK,EAAE;QAC9Ba,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEf,gBAAgB,CAAC;MAC7C;MAEA,IAAIE,gBAAgB,KAAK,eAAe,EAAE;QACxCW,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEb,gBAAgB,CAAC;MAC7C;MAEA,IAAIE,WAAW,CAACY,IAAI,CAAC,CAAC,EAAE;QACtBH,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEX,WAAW,CAACY,IAAI,CAAC,CAAC,CAAC;MAC7C;MAEA,MAAMC,QAAQ,GAAG,MAAM7B,KAAK,CAAC8B,GAAG,CAAC,kCAAkCL,MAAM,CAACM,QAAQ,CAAC,CAAC,EAAE,EAAE;QACtFC,eAAe,EAAE;MACnB,CAAC,CAAC;MACFvB,OAAO,CAACoB,QAAQ,CAACI,IAAI,CAACzB,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAO0B,GAAG,EAAE;MACZb,QAAQ,CAAC,sBAAsB,CAAC;MAChCc,OAAO,CAACf,KAAK,CAAC,sBAAsB,EAAEc,GAAG,CAAC;IAC5C,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,UAAU,GAAGA,CAAA,KAAM;IACvB9B,QAAQ,CAAC,OAAO,CAAC;EACnB,CAAC;EAED,MAAM+B,oBAAoB,GAAIC,QAAQ,IAAK;IACzCzB,mBAAmB,CAACyB,QAAQ,CAAC;EAC/B,CAAC;EAED,MAAMC,oBAAoB,GAAIC,CAAC,IAAK;IAClCzB,mBAAmB,CAACyB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EACrC,CAAC;EAED,MAAMC,kBAAkB,GAAIH,CAAC,IAAK;IAChCvB,cAAc,CAACuB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAChC,CAAC;EAED,MAAME,eAAe,GAAIC,MAAM,IAAK;IAClCvC,QAAQ,CAAC,SAASuC,MAAM,EAAE,CAAC;EAC7B,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BxC,QAAQ,CAAC,aAAa,CAAC;EACzB,CAAC;EAED,MAAMyC,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,IAAI,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;;IAEnD;IACA,MAAMG,MAAM,GAAGR,IAAI,GAAG,EAAE;IACxB,OAAO,GAAGA,IAAI,IAAIE,KAAK,IAAII,GAAG,KAAKE,MAAM,IAAIN,KAAK,IAAII,GAAG,OAAO;EAClE,CAAC;EAED,MAAMG,sBAAsB,GAAItB,QAAQ,IAAK;IAC3C,OAAOA,QAAQ,CAACuB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGxB,QAAQ,CAACyB,KAAK,CAAC,CAAC,CAAC;EAC7D,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG1C,cAAc;EAExC,oBACEnB,OAAA;IAAK8D,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7B/D,OAAA;MAAK8D,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB/D,OAAA;QAAK8D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B/D,OAAA;UAAQ8D,SAAS,EAAC,eAAe;UAACE,OAAO,EAAE/B,UAAW;UAAA8B,QAAA,eACpD/D,OAAA;YAAKiE,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC7C/D,OAAA;cAAMoE,CAAC,EAAC,8DAA8D;cAACC,IAAI,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACTzE,OAAA;UAAA+D,QAAA,EAAI;QAAI;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACbzE,OAAA;UAAK8D,SAAS,EAAC,qBAAqB;UAAAC,QAAA,GACjC3D,IAAI,IAAIA,IAAI,CAACsE,IAAI,KAAK,OAAO,iBAC5B1E,OAAA;YAAQ8D,SAAS,EAAC,WAAW;YAACE,OAAO,EAAErB,gBAAiB;YAAAoB,QAAA,eACtD/D,OAAA;cAAKiE,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACE,IAAI,EAAC,OAAO;cAAAN,QAAA,eAC1D/D,OAAA;gBAAMoE,CAAC,EAAC;cAAuH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9H;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CACT,eACDzE,OAAA;YAAQ8D,SAAS,EAAC,YAAY;YAAAC,QAAA,eAC5B/D,OAAA;cAAKiE,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACE,IAAI,EAAC,OAAO;cAAAN,QAAA,eAC1D/D,OAAA;gBAAMoE,CAAC,EAAC;cAAkJ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzE,OAAA;QAAK8D,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpC/D,OAAA;UAAK8D,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClC/D,OAAA;YACE2E,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,QAAQ;YACpBrC,KAAK,EAAE1B,WAAY;YACnBgE,QAAQ,EAAErC,kBAAmB;YAC7BsB,SAAS,EAAC;UAAmB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACFzE,OAAA;YAAK8D,SAAS,EAAC,kBAAkB;YAACG,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACE,IAAI,EAAC,MAAM;YAAAN,QAAA,eACtF/D,OAAA;cAAMoE,CAAC,EAAC,4IAA4I;cAACU,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzE,OAAA;QAAK8D,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtC/D,OAAA;UACEuC,KAAK,EAAE5B,gBAAiB;UACxBkE,QAAQ,EAAEzC,oBAAqB;UAC/B0B,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAE/B3C,SAAS,CAAC8D,GAAG,CAACC,QAAQ,iBACrBnF,OAAA;YAAuBuC,KAAK,EAAE4C,QAAS;YAAApB,QAAA,EACpCoB;UAAQ,GADEA,QAAQ;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEb,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACTzE,OAAA;UAAK8D,SAAS,EAAC,wBAAwB;UAACG,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACE,IAAI,EAAC,MAAM;UAAAN,QAAA,eAC5F/D,OAAA;YAAMoE,CAAC,EAAC,gBAAgB;YAACC,IAAI,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzE,OAAA;QAAK8D,SAAS,EAAC,2BAA2B;QAAAC,QAAA,eACxC/D,OAAA;UAAK8D,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EACpCF,iBAAiB,CAACqB,GAAG,CAAC/C,QAAQ,iBAC7BnC,OAAA;YAEE8D,SAAS,EAAE,gBAAgBrD,gBAAgB,KAAK0B,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC3E6B,OAAO,EAAEA,CAAA,KAAM9B,oBAAoB,CAACC,QAAQ,CAAE;YAAA4B,QAAA,EAE7CN,sBAAsB,CAACtB,QAAQ;UAAC,GAJ5BA,QAAQ;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKP,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzE,OAAA;QAAK8D,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BhD,OAAO,gBACNf,OAAA;UAAK8D,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B/D,OAAA;YAAK8D,SAAS,EAAC;UAAiB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvCzE,OAAA;YAAA+D,QAAA,EAAG;UAAe;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,GACJxD,KAAK,gBACPjB,OAAA;UAAK8D,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB/D,OAAA;YAAA+D,QAAA,EAAI9C;UAAK;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACdzE,OAAA;YAAQgE,OAAO,EAAE3C,SAAU;YAACyC,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,GACJpE,IAAI,CAAC+E,MAAM,KAAK,CAAC,gBACnBpF,OAAA;UAAK8D,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzB/D,OAAA;YAAA+D,QAAA,EAAG;UAAwC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,gBAENzE,OAAA;UAAK8D,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB1D,IAAI,CAAC6E,GAAG,CAAEG,IAAI,iBACbrF,OAAA;YAEE8D,SAAS,EAAC,WAAW;YACrBE,OAAO,EAAEA,CAAA,KAAMvB,eAAe,CAAC4C,IAAI,CAACC,GAAG,CAAE;YAAAvB,QAAA,gBAEzC/D,OAAA;cAAK8D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC/D,OAAA;gBAAK8D,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC5BnB,UAAU,CAACyC,IAAI,CAACE,SAAS;cAAC;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACNzE,OAAA;gBAAK8D,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAC7BsB,IAAI,CAACG;cAAK;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,EACLY,IAAI,CAACI,OAAO,iBACXzF,OAAA;gBAAK8D,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,GAC/BsB,IAAI,CAACI,OAAO,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KAClC;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,eACDzE,OAAA;gBAAK8D,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B/D,OAAA;kBAAM8D,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEN,sBAAsB,CAAC4B,IAAI,CAAClD,QAAQ;gBAAC;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC7EY,IAAI,CAACF,QAAQ,KAAK,eAAe,iBAChCnF,OAAA;kBAAM8D,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEsB,IAAI,CAACF;gBAAQ;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACtD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLY,IAAI,CAACM,KAAK,iBACT3F,OAAA;cAAK8D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B/D,OAAA;gBACE4F,GAAG,EAAE,wBAAwBP,IAAI,CAACM,KAAK,CAACE,GAAG,EAAG;gBAC9CC,GAAG,EAAET,IAAI,CAACG,KAAM;gBAChBO,OAAO,EAAG1D,CAAC,IAAK;kBACdA,CAAC,CAACC,MAAM,CAAC0D,KAAK,CAACC,OAAO,GAAG,MAAM;gBACjC;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA,GAjCIY,IAAI,CAACC,GAAG;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkCV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvE,EAAA,CAxQID,IAAI;EAAA,QACSN,WAAW,EACXC,OAAO;AAAA;AAAAsG,EAAA,GAFpBjG,IAAI;AA0QV,eAAeA,IAAI;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}