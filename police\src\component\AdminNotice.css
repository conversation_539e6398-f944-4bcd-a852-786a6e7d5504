/* Admin Notice Styles - Based on AdminNews.css */
.admin-notice-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.admin-notice-card {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.admin-notice-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #1976D2;
  color: white;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  min-height: 56px;
  width: 100%;
  box-sizing: border-box;
}

.admin-notice-header h1 {
  font-size: 18px;
  margin: 0;
  font-weight: 500;
  text-align: center;
  flex: 1;
  color: white;
}

.admin-notice-back-btn,
.add-notice-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  transition: background-color 0.2s ease;
}

.admin-notice-back-btn:hover,
.add-notice-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.notice-form-container {
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  padding: 20px;
}

.notice-form {
  max-width: 600px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  outline: none;
  transition: border-color 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  border-color: #1976D2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.image-preview {
  margin-top: 10px;
  max-width: 200px;
}

.image-preview img {
  width: 100%;
  height: auto;
  border-radius: 6px;
  border: 1px solid #ddd;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
}

.cancel-btn,
.submit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.cancel-btn:hover {
  background: #e0e0e0;
}

.submit-btn {
  background: #1976D2;
  color: white;
}

.submit-btn:hover {
  background: #1565C0;
}

.admin-notice-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.admin-notice-loading,
.admin-notice-error,
.admin-notice-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.admin-notice-error {
  color: #d32f2f;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1976D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-btn {
  margin-top: 16px;
  padding: 8px 16px;
  background: #1976D2;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: #1565C0;
}

.admin-notice-list {
  padding: 0;
}

.admin-notice-item {
  display: flex;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  gap: 12px;
}

.admin-notice-item:last-child {
  border-bottom: none;
}

.admin-notice-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.admin-notice-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-notice-item-date {
  font-size: 12px;
  color: #666;
  font-weight: 400;
}

.admin-notice-item-actions {
  display: flex;
  gap: 8px;
}

.edit-btn,
.delete-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.edit-btn {
  color: #1976D2;
}

.edit-btn:hover {
  background: rgba(25, 118, 210, 0.1);
}

.delete-btn {
  color: #d32f2f;
}

.delete-btn:hover {
  background: rgba(211, 47, 47, 0.1);
}

.admin-notice-item-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.admin-notice-item-preview {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.admin-notice-item-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.notice-category,
.notice-province,
.notice-priority {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.notice-category {
  background: #e3f2fd;
  color: #1976D2;
}

.notice-province {
  background: #f3e5f5;
  color: #7b1fa2;
}

.notice-priority {
  background: #fff3e0;
  color: #f57c00;
}

.priority-high {
  background: #ffebee;
  color: #d32f2f;
}

.priority-low {
  background: #e8f5e8;
  color: #388e3c;
}

.admin-notice-item-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #666;
}

.admin-notice-item-image {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f5f5;
}

.admin-notice-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media (max-width: 768px) {
  .admin-notice-container {
    padding: 8px;
  }

  .admin-notice-card {
    min-height: calc(100vh - 16px);
  }

  .notice-form-container {
    padding: 16px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .admin-notice-item {
    padding: 12px;
  }

  .admin-notice-item-image {
    width: 60px;
    height: 60px;
  }
}

@media (max-width: 480px) {
  .admin-notice-header {
    padding: 8px 12px;
  }

  .admin-notice-header h1 {
    font-size: 16px;
  }

  .notice-form-container {
    padding: 12px;
  }

  .form-actions {
    flex-direction: column;
  }

  .cancel-btn,
  .submit-btn {
    width: 100%;
  }
}
