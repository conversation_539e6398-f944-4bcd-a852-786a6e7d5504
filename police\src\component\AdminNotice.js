import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';
import './AdminNotice.css';

const AdminNotice = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [notices, setNotices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [editingNotice, setEditingNotice] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    category: 'promotion',
    province: 'All Provinces',
    priority: 'medium'
  });
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState('');

  const noticeCategories = [
    'promotion',
    'transfer notice',
    'directives',
    'rules',
    'exams',
    'order',
    'general notice',
    'law',
    'un notice',
    'deputation',
    'other notice(career)',
    'bipad notice',
    'public procurement',
    'ordinance',
    'procedure'
  ];

  const provinces = [
    'All Provinces',
    'Province 1',
    'Madhesh Province',
    'Bagmati Province',
    'Gandaki Province',
    'Lumbini Province',
    'Karnali Province',
    'Sudurpashchim Province'
  ];

  useEffect(() => {
    if (!user || user.role !== 'admin') {
      navigate('/login');
      return;
    }
    fetchNotices();
  }, [user, navigate]);

  const fetchNotices = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:5000/api/notice', {
        withCredentials: true
      });
      setNotices(response.data.notices || []);
    } catch (err) {
      setError('Failed to fetch notices');
      console.error('Error fetching notices:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/notice');
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      console.log('Form data being submitted:', formData);
      console.log('Selected image:', selectedImage);

      const submitData = new FormData();
      submitData.append('title', formData.title);
      submitData.append('content', formData.content);
      submitData.append('category', formData.category);
      submitData.append('province', formData.province);
      submitData.append('priority', formData.priority);

      if (selectedImage) {
        submitData.append('image', selectedImage);
      }

      console.log('Submitting to:', editingNotice ? 'PUT' : 'POST');

      if (editingNotice) {
        const response = await axios.put(`http://localhost:5000/api/notice/${editingNotice._id}`, submitData, {
          withCredentials: true,
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
        console.log('Update response:', response.data);
      } else {
        const response = await axios.post('http://localhost:5000/api/notice', submitData, {
          withCredentials: true,
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
        console.log('Create response:', response.data);
      }

      // Reset form
      setFormData({
        title: '',
        content: '',
        category: 'promotion',
        province: 'All Provinces',
        priority: 'medium'
      });
      setSelectedImage(null);
      setImagePreview('');
      setShowForm(false);
      setEditingNotice(null);
      
      // Refresh notices
      fetchNotices();
    } catch (err) {
      console.error('Error saving notice:', err);
      console.error('Error response:', err.response?.data);
      console.error('Error status:', err.response?.status);
      setError(`Failed to save notice: ${err.response?.data?.message || err.message}`);
    }
  };

  const handleEdit = (notice) => {
    setEditingNotice(notice);
    setFormData({
      title: notice.title,
      content: notice.content,
      category: notice.category,
      province: notice.province,
      priority: notice.priority
    });
    if (notice.image) {
      setImagePreview(`http://localhost:5000${notice.image.url}`);
    }
    setShowForm(true);
  };

  const handleDelete = async (noticeId) => {
    if (window.confirm('Are you sure you want to delete this notice?')) {
      try {
        await axios.delete(`http://localhost:5000/api/notice/${noticeId}`, {
          withCredentials: true
        });
        fetchNotices();
      } catch (err) {
        console.error('Error deleting notice:', err);
        setError('Failed to delete notice');
      }
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    // Simple B.S. conversion (approximate)
    const bsYear = year + 57;
    return `${year}-${month}-${day} (${bsYear}-${month}-${day} B.S)`;
  };

  const getCategoryDisplayName = (category) => {
    return category.split(' ').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  if (!user || user.role !== 'admin') {
    return null;
  }

  return (
    <div className="admin-notice-container">
      <div className="admin-notice-card">
        <div className="admin-notice-header">
          <button className="admin-notice-back-btn" onClick={handleBack}>
            <svg width="24" height="24" viewBox="0 0 24 24">
              <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" fill="white"/>
            </svg>
          </button>
          <h1>Admin - Notice Management</h1>
          <button 
            className="add-notice-btn"
            onClick={() => {
              setShowForm(true);
              setEditingNotice(null);
              setFormData({
                title: '',
                content: '',
                category: 'promotion',
                province: 'All Provinces',
                priority: 'medium'
              });
              setSelectedImage(null);
              setImagePreview('');
            }}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
            </svg>
          </button>
        </div>

        {showForm && (
          <div className="notice-form-container">
            <form onSubmit={handleSubmit} className="notice-form">
              <div className="form-group">
                <label htmlFor="title">Title *</label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                  placeholder="Enter notice title"
                />
              </div>

              <div className="form-group">
                <label htmlFor="content">Content *</label>
                <textarea
                  id="content"
                  name="content"
                  value={formData.content}
                  onChange={handleInputChange}
                  required
                  rows="6"
                  placeholder="Enter notice content"
                />
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="category">Category *</label>
                  <select
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    required
                  >
                    {noticeCategories.map(category => (
                      <option key={category} value={category}>
                        {getCategoryDisplayName(category)}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label htmlFor="province">Province</label>
                  <select
                    id="province"
                    name="province"
                    value={formData.province}
                    onChange={handleInputChange}
                  >
                    {provinces.map(province => (
                      <option key={province} value={province}>
                        {province}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="priority">Priority</label>
                <select
                  id="priority"
                  name="priority"
                  value={formData.priority}
                  onChange={handleInputChange}
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="image">Image (Optional)</label>
                <input
                  type="file"
                  id="image"
                  accept="image/*"
                  onChange={handleImageChange}
                />
                {imagePreview && (
                  <div className="image-preview">
                    <img src={imagePreview} alt="Preview" />
                  </div>
                )}
              </div>

              <div className="form-actions">
                <button type="button" onClick={() => setShowForm(false)} className="cancel-btn">
                  Cancel
                </button>
                <button type="submit" className="submit-btn">
                  {editingNotice ? 'Update Notice' : 'Create Notice'}
                </button>
              </div>
            </form>
          </div>
        )}

        <div className="admin-notice-content">
          {loading ? (
            <div className="admin-notice-loading">
              <div className="loading-spinner"></div>
              <p>Loading notices...</p>
            </div>
          ) : error ? (
            <div className="admin-notice-error">
              <p>{error}</p>
              <button onClick={fetchNotices} className="retry-btn">Retry</button>
            </div>
          ) : notices.length === 0 ? (
            <div className="admin-notice-empty">
              <p>No notices found. Create your first notice!</p>
            </div>
          ) : (
            <div className="admin-notice-list">
              {notices.map((notice) => (
                <div key={notice._id} className="admin-notice-item">
                  <div className="admin-notice-item-content">
                    <div className="admin-notice-item-header">
                      <div className="admin-notice-item-date">
                        {formatDate(notice.createdAt)}
                      </div>
                      <div className="admin-notice-item-actions">
                        <button 
                          onClick={() => handleEdit(notice)}
                          className="edit-btn"
                          title="Edit"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                          </svg>
                        </button>
                        <button 
                          onClick={() => handleDelete(notice._id)}
                          className="delete-btn"
                          title="Delete"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                          </svg>
                        </button>
                      </div>
                    </div>
                    <div className="admin-notice-item-title">
                      {notice.title}
                    </div>
                    <div className="admin-notice-item-preview">
                      {notice.content.substring(0, 150)}...
                    </div>
                    <div className="admin-notice-item-meta">
                      <span className="notice-category">{getCategoryDisplayName(notice.category)}</span>
                      {notice.province !== 'All Provinces' && (
                        <span className="notice-province">{notice.province}</span>
                      )}
                      <span className="notice-priority priority-{notice.priority}">{notice.priority}</span>
                    </div>
                    <div className="admin-notice-item-stats">
                      <span>Views: {notice.views || 0}</span>
                      <span>Likes: {notice.likes || 0}</span>
                    </div>
                  </div>
                  {notice.image && (
                    <div className="admin-notice-item-image">
                      <img 
                        src={`http://localhost:5000${notice.image.url}`} 
                        alt={notice.title}
                        onError={(e) => {
                          e.target.style.display = 'none';
                        }}
                      />
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminNotice;
