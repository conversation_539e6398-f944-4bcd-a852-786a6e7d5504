/* Bottom Navigation - Exact Match to Screenshot */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 400px;
  background: #1976D2;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 12px 20px;
  border-radius: 20px 20px 0 0;
  z-index: 1000;
  height: 70px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.nav-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 8px 16px;
  transition: none;
  flex: 1;
  max-width: 80px;
}

/* Remove all hover effects */
.nav-btn:hover {
  /* No hover effects */
}

.nav-btn.active {
  color: #FFD700;
}

.nav-btn.active span {
  color: #FFD700;
}

.nav-btn img {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
  opacity: 0.7;
}

.nav-btn.active img {
  opacity: 1;
  filter: brightness(0) invert(1) sepia(1) saturate(10) hue-rotate(45deg);
}

.nav-btn span {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
  margin-top: 2px;
  text-align: center;
  line-height: 1.2;
}

/* Special styling for Public Eye button (middle button) */
.nav-btn:nth-child(2) {
  background: #0088cc;
  width: 60px;
  height: 60px;
  padding: 0;
  border-radius: 50%;
  margin-bottom: 30px;
  transform: translateY(-15px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  position: relative;
  flex: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:nth-child(2) img {
  width: 28px;
  height: 28px;
  filter: brightness(0) invert(1);
  opacity: 1;
}

.nav-btn:nth-child(2) span {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
}

/* Responsive Design for Bottom Navigation */
@media (max-width: 480px) {
  .bottom-nav {
    max-width: 100%;
    padding: 10px 0;
  }
  
  .nav-btn {
    padding: 6px 8px;
    max-width: 70px;
    min-height: 55px;
  }
  
  .nav-btn img {
    width: 20px;
    height: 20px;
  }
  
  .nav-btn span {
    font-size: 11px;
  }
}

@media (min-width: 768px) {
  .bottom-nav {
    max-width: 500px;
    padding: 16px 0;
    border-radius: 12px 12px 0 0;
  }
  
  .nav-btn {
    padding: 12px 16px;
    max-width: 100px;
    min-height: 70px;
  }
  
  .nav-btn img {
    width: 28px;
    height: 28px;
  }
  
  .nav-btn span {
    font-size: 13px;
  }
}

@media (min-width: 1024px) {
  .bottom-nav {
    max-width: 600px;
    padding: 20px 0;
  }
  
  .nav-btn {
    padding: 16px 20px;
    max-width: 120px;
    min-height: 80px;
  }
  
  .nav-btn img {
    width: 32px;
    height: 32px;
  }
  
  .nav-btn span {
    font-size: 14px;
  }
}

/* Ensure proper spacing from bottom for content */
.page-content {
  padding-bottom: 80px;
}

@media (min-width: 768px) {
  .page-content {
    padding-bottom: 100px;
  }
}

@media (min-width: 1024px) {
  .page-content {
    padding-bottom: 120px;
  }
}
