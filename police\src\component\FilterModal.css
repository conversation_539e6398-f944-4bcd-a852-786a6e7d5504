.filter-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 16px;
}

.filter-modal-card {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.filter-modal-header {
  background: #0088cc;
  color: white;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 60px;
}

.filter-modal-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  flex: 1;
  text-align: center;
}

.filter-close-btn,
.filter-save-btn {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
  min-width: 60px;
  width: auto;
  height: auto;
}

.filter-close-btn {
  font-size: 24px;
  font-weight: bold;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-save-btn {
  width: auto;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
}

.filter-close-btn:hover,
.filter-save-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 6px;
}

.filter-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.filter-section {
  margin-bottom: 24px;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-section h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.date-picker-section {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.date-input-group {
  flex: 1;
  min-width: 150px;
}

.date-input-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #555;
}

.date-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #333;
  transition: border-color 0.2s ease;
}

.date-input:focus {
  outline: none;
  border-color: #0088cc;
  box-shadow: 0 0 0 2px rgba(0, 136, 204, 0.1);
}

.filter-options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.filter-option-btn {
  padding: 10px 8px;
  border: 1px solid #ddd;
  border-radius: 12px;
  background: #f5f5f5;
  color: #666;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.filter-option-btn:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

.filter-option-btn.active {
  background: #0088cc;
  color: white;
  border-color: #0088cc;
  box-shadow: 0 2px 4px rgba(0, 136, 204, 0.2);
}

.filter-option-btn.active:hover {
  background: #0077bb;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .filter-modal-overlay {
    padding: 8px;
  }
  
  .filter-modal-card {
    max-height: 90vh;
  }
  
  .filter-modal-header {
    padding: 12px 16px;
  }
  
  .filter-modal-header h2 {
    font-size: 16px;
  }
  
  .filter-modal-content {
    padding: 16px;
  }
  
  .filter-options-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 6px;
  }
  
  .filter-option-btn {
    padding: 8px 10px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .filter-options-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .filter-option-btn {
    padding: 10px 8px;
    font-size: 11px;
  }
}
