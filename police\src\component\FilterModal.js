import React from 'react';
import './FilterModal.css';

const FilterModal = ({ 
  isOpen, 
  onClose, 
  selectedCategory, 
  selectedProvince, 
  onCategoryChange, 
  onProvinceChange,
  onApply 
}) => {
  if (!isOpen) return null;

  const newsCategories = [
    'all',
    'fire',
    'cyber crime',
    'informative',
    'harmful weapon',
    'flood landslide',
    'organizational program',
    'gambling',
    'dead bodies found',
    'rape',
    'home ministry program',
    'Narcotic',
    'IGP program',
    'blackmailing',
    'Quarrel/Disturbance',
    'Bribery',
    'drug',
    'violence',
    'Suspicious thing',
    'crime report',
    'burglary',
    'pick pocketing',
    'harassment',
    'illegal trading',
    'police day program',
    'misbehaviour',
    'robbery',
    'public gathering',
    'crime(arrest)',
    'human trafficking',
    'miscellaneous'
  ];

  const provinces = [
    'All Provinces',
    'Province 1',
    'Madhesh Province',
    'Bagmati Province',
    'Gandaki Province',
    'Lumbini Province',
    'Karnali Province',
    'Sudurpashchim Province'
  ];

  const getCategoryDisplayName = (category) => {
    if (category === 'all') return 'All Categories';
    return category.split(' ').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const handleApply = () => {
    onApply();
    onClose();
  };

  return (
    <div className="filter-modal-overlay" onClick={onClose}>
      <div className="filter-modal-card" onClick={(e) => e.stopPropagation()}>
        <div className="filter-modal-header">
          <button className="filter-close-btn" onClick={onClose}>×</button>
          <h2>Select Filters</h2>
          <button className="filter-save-btn" onClick={handleApply}>Apply</button>
        </div>

        <div className="filter-modal-content">
          <div className="filter-section">
            <h3>Category</h3>
            <div className="filter-options-grid">
              {newsCategories.map(category => (
                <button
                  key={category}
                  className={`filter-option-btn ${selectedCategory === category ? 'active' : ''}`}
                  onClick={() => onCategoryChange(category)}
                >
                  {getCategoryDisplayName(category)}
                </button>
              ))}
            </div>
          </div>

          <div className="filter-section">
            <h3>Province</h3>
            <div className="filter-options-grid">
              {provinces.map(province => (
                <button
                  key={province}
                  className={`filter-option-btn ${selectedProvince === province ? 'active' : ''}`}
                  onClick={() => onProvinceChange(province)}
                >
                  {province}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterModal;
