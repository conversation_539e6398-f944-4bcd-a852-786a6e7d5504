import React from 'react';
import './FilterModal.css';

const FilterModal = ({
  isOpen,
  onClose,
  selectedCategory,
  selectedProvince,
  onCategoryChange,
  onProvinceChange,
  onApply
}) => {
  const [startDate, setStartDate] = React.useState('');
  const [endDate, setEndDate] = React.useState('');

  if (!isOpen) return null;

  const provinces = [
    'All Provinces',
    'Province 1',
    'Madhesh Province',
    'Bagmati Province',
    'Gandaki Province',
    'Lumbini Province',
    'Karnali Province',
    'Sudurpashchim Province'
  ];



  const handleApply = () => {
    onApply();
    onClose();
  };

  return (
    <div className="filter-modal-overlay" onClick={onClose}>
      <div className="filter-modal-card" onClick={(e) => e.stopPropagation()}>
        <div className="filter-modal-header">
          <button className="filter-close-btn" onClick={onClose}>×</button>
          <h2>Select Filters</h2>
          <button className="filter-save-btn" onClick={handleApply}>Apply</button>
        </div>

        <div className="filter-modal-content">
          <div className="filter-section">
            <h3>Select Date Range</h3>
            <div className="date-picker-section">
              <div className="date-input-group">
                <label>Start Date</label>
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="date-input"
                />
              </div>
              <div className="date-input-group">
                <label>End Date</label>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="date-input"
                  min={startDate}
                />
              </div>
            </div>
          </div>

          <div className="filter-section">
            <h3>Province</h3>
            <div className="filter-options-grid">
              {provinces.map(province => (
                <button
                  key={province}
                  className={`filter-option-btn ${selectedProvince === province ? 'active' : ''}`}
                  onClick={() => onProvinceChange(province)}
                >
                  {province}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterModal;
