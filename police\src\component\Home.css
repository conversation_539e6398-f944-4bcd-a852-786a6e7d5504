.home-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 16px;
  padding-bottom: 0;
  display: flex;
  flex-direction: column;
  max-width: 400px;
  margin: 0 auto;
}

.home-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
}

.home-header {
  padding: 20px;
}

.logo-section {
  display: flex;
  flex-direction: column;
}

.logo-text-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  margin-bottom: 16px;
  padding-top: 0; /* Add this if there's top padding/margin */
}

.logo {
  width: 100px; /* Reduce size if needed */
  height: auto;
  object-fit: contain;
  margin-top: -10px; /* Pull it up */
}


.header-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
}


.sign-in-btn {
  background: #0088cc;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  width: auto; /* ✅ prevent full width */
  min-width: 80px;
}

.home-card .header-actions .notification-btn {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  min-width: auto;
  box-shadow: none;
  margin: 0;
}

.home-card .header-actions .notification-btn:hover {
  background: white;
}

.home-card .header-actions .notification-btn img {
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

.home-card .header-actions .notification-btn:hover img {
  transform: scale(1.4);
}

.header-text {
  padding-left: 4px;
}

.header-text h1 {
  font-size: 24px;
  margin: 0;
  color: #333;
  font-weight: bold;
  margin-bottom: 8px;
  text-align: left;
}

.header-text p {
  margin: 0;
  color: #666;
  font-size: 15px;
  line-height: 1.4;
  text-align: left;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  padding: 24px;
  padding-bottom: 24px;
}

.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-align: center;
  cursor: pointer;
}

.service-item img {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.service-item p {
  margin: 0;
  font-size: 13px;
  color: #333;
  white-space: pre-line;
  line-height: 1.3;
}

/* Bottom Navigation - Part of Home Container */
.home-bottom-nav {
  background: #1976D2 !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: flex-end !important;
  padding: 8px 20px !important;
  border-radius: 0 0 12px 12px !important;
  height: 60px !important;
  margin-top: auto !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.home-nav-btn {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  gap: 4px !important;
  background: none !important;
  border: none !important;
  color: rgba(255, 255, 255, 0.7) !important;
  cursor: pointer !important;
  padding: 8px 16px !important;
  transition: none !important;
  flex: 1 !important;
  max-width: 80px !important;
}

.home-nav-btn img {
  width: 24px !important;
  height: 24px !important;
  filter: brightness(0) invert(1) !important;
  opacity: 0.7 !important;
}

.home-nav-btn span {
  font-size: 12px !important;
  color: rgba(255, 255, 255, 0.7) !important;
  font-weight: 400 !important;
  margin-top: 2px !important;
}

.home-nav-btn.active {
  color: #FFD700 !important;
}

.home-nav-btn.active span {
  color: #FFD700 !important;
}

.home-nav-btn.active img {
  opacity: 1 !important;
  filter: brightness(0) invert(1) sepia(1) saturate(10) hue-rotate(45deg) !important;
}

/* Special styling for Public Eye button (middle button) */
.home-nav-btn:nth-child(2) {
  background: #0088cc !important;
  width: 50px !important;
  height: 50px !important;
  padding: 0 !important;
  border-radius: 50% !important;
  margin-bottom: 20px !important;
  transform: translateY(-10px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  position: relative !important;
  flex: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.home-nav-btn:nth-child(2) img {
  width: 24px !important;
  height: 24px !important;
  filter: brightness(0) invert(1) !important;
  opacity: 1 !important;
}

.home-nav-btn:nth-child(2) span {
  position: absolute !important;
  bottom: -20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  white-space: nowrap !important;
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 11px !important;
}

/* Responsive Design for Bottom Navigation */
@media (max-width: 480px) {
  .home-bottom-nav {
    padding: 8px 16px !important;
    height: 55px !important;
  }

  .home-nav-btn {
    padding: 6px 12px !important;
    max-width: 70px !important;
  }

  .home-nav-btn img {
    width: 20px !important;
    height: 20px !important;
  }

  .home-nav-btn span {
    font-size: 10px !important;
  }

  .home-nav-btn:nth-child(2) {
    width: 45px !important;
    height: 45px !important;
    margin-bottom: 15px !important;
    transform: translateY(-8px) !important;
  }

  .home-nav-btn:nth-child(2) img {
    width: 22px !important;
    height: 22px !important;
  }

  .home-nav-btn:nth-child(2) span {
    bottom: -18px !important;
    font-size: 10px !important;
  }
}

@media (min-width: 768px) {
  .home-bottom-nav {
    padding: 10px 25px !important;
    height: 65px !important;
    border-radius: 0 0 12px 12px !important;
  }

  .home-nav-btn {
    padding: 8px 18px !important;
    max-width: 90px !important;
  }

  .home-nav-btn img {
    width: 24px !important;
    height: 24px !important;
  }

  .home-nav-btn span {
    font-size: 12px !important;
  }

  .home-nav-btn:nth-child(2) {
    width: 55px !important;
    height: 55px !important;
    margin-bottom: 25px !important;
    transform: translateY(-12px) !important;
  }

  .home-nav-btn:nth-child(2) img {
    width: 26px !important;
    height: 26px !important;
  }

  .home-nav-btn:nth-child(2) span {
    bottom: -22px !important;
    font-size: 12px !important;
  }
}

@media (min-width: 1024px) {
  .home-bottom-nav {
    padding: 12px 30px !important;
    height: 70px !important;
  }

  .home-nav-btn {
    padding: 10px 20px !important;
    max-width: 100px !important;
  }

  .home-nav-btn img {
    width: 26px !important;
    height: 26px !important;
  }

  .home-nav-btn span {
    font-size: 13px !important;
  }

  .home-nav-btn:nth-child(2) {
    width: 60px !important;
    height: 60px !important;
    margin-bottom: 30px !important;
    transform: translateY(-15px) !important;
  }

  .home-nav-btn:nth-child(2) img {
    width: 28px !important;
    height: 28px !important;
  }

  .home-nav-btn:nth-child(2) span {
    bottom: -25px !important;
    font-size: 13px !important;
  }
}