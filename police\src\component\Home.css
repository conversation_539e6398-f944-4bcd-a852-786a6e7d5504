.home-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 16px;
  padding-bottom: 0;
  display: flex;
  flex-direction: column;
  max-width: 400px;
  margin: 0 auto;
}

.home-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 600px;
  margin: 0 auto;
  width: 100%;
  position: relative;
}

.home-header {
  padding: 20px;
}

.logo-section {
  display: flex;
  flex-direction: column;
}

.logo-text-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  margin-bottom: 16px;
  padding-top: 0; /* Add this if there's top padding/margin */
}

.logo {
  width: 100px; /* Reduce size if needed */
  height: auto;
  object-fit: contain;
  margin-top: -10px; /* Pull it up */
}


.header-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
}


.sign-in-btn {
  background: #0088cc;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  width: auto; /* ✅ prevent full width */
  min-width: 80px;
}

.home-card .header-actions .notification-btn {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  min-width: auto;
  box-shadow: none;
  margin: 0;
}

.home-card .header-actions .notification-btn:hover {
  background: white;
}

.home-card .header-actions .notification-btn img {
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

.home-card .header-actions .notification-btn:hover img {
  transform: scale(1.4);
}

.header-text {
  padding-left: 4px;
}

.header-text h1 {
  font-size: 24px;
  margin: 0;
  color: #333;
  font-weight: bold;
  margin-bottom: 8px;
  text-align: left;
}

.header-text p {
  margin: 0;
  color: #666;
  font-size: 15px;
  line-height: 1.4;
  text-align: left;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  padding: 24px;
  padding-bottom: 24px;
}

.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-align: center;
  cursor: pointer;
}

.service-item img {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.service-item p {
  margin: 0;
  font-size: 13px;
  color: #333;
  white-space: pre-line;
  line-height: 1.3;
}

/* Bottom Navigation - Part of Home Container */
.bottom-nav {
  background: #1976D2;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 8px 20px;
  border-radius: 20px 20px 0 0;
  height: 60px;
  margin-top: auto;
}

.nav-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 8px 16px;
  transition: none;
  flex: 1;
  max-width: 80px;
}

.nav-btn img {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
  opacity: 0.7;
}

.nav-btn span {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
  margin-top: 2px;
}

.nav-btn.active {
  color: #FFD700;
}

.nav-btn.active span {
  color: #FFD700;
}

.nav-btn.active img {
  opacity: 1;
  filter: brightness(0) invert(1) sepia(1) saturate(10) hue-rotate(45deg);
}

/* Special styling for Public Eye button (middle button) */
.nav-btn:nth-child(2) {
  background: #0088cc;
  width: 50px;
  height: 50px;
  padding: 0;
  border-radius: 50%;
  margin-bottom: 20px;
  transform: translateY(-10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  position: relative;
  flex: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:nth-child(2) img {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
  opacity: 1;
}

.nav-btn:nth-child(2) span {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  color: rgba(255, 255, 255, 0.9);
  font-size: 11px;
}

/* Responsive Design for Bottom Navigation */
@media (max-width: 480px) {
  .bottom-nav {
    padding: 8px 16px;
    height: 55px;
  }

  .nav-btn {
    padding: 6px 12px;
    max-width: 70px;
  }

  .nav-btn img {
    width: 20px;
    height: 20px;
  }

  .nav-btn span {
    font-size: 10px;
  }

  .nav-btn:nth-child(2) {
    width: 45px;
    height: 45px;
    margin-bottom: 15px;
    transform: translateY(-8px);
  }

  .nav-btn:nth-child(2) img {
    width: 22px;
    height: 22px;
  }

  .nav-btn:nth-child(2) span {
    bottom: -18px;
    font-size: 10px;
  }
}

@media (min-width: 768px) {
  .bottom-nav {
    padding: 10px 25px;
    height: 65px;
    border-radius: 25px 25px 0 0;
  }

  .nav-btn {
    padding: 8px 18px;
    max-width: 90px;
  }

  .nav-btn img {
    width: 24px;
    height: 24px;
  }

  .nav-btn span {
    font-size: 12px;
  }

  .nav-btn:nth-child(2) {
    width: 55px;
    height: 55px;
    margin-bottom: 25px;
    transform: translateY(-12px);
  }

  .nav-btn:nth-child(2) img {
    width: 26px;
    height: 26px;
  }

  .nav-btn:nth-child(2) span {
    bottom: -22px;
    font-size: 12px;
  }
}

@media (min-width: 1024px) {
  .bottom-nav {
    padding: 12px 30px;
    height: 70px;
  }

  .nav-btn {
    padding: 10px 20px;
    max-width: 100px;
  }

  .nav-btn img {
    width: 26px;
    height: 26px;
  }

  .nav-btn span {
    font-size: 13px;
  }

  .nav-btn:nth-child(2) {
    width: 60px;
    height: 60px;
    margin-bottom: 30px;
    transform: translateY(-15px);
  }

  .nav-btn:nth-child(2) img {
    width: 28px;
    height: 28px;
  }

  .nav-btn:nth-child(2) span {
    bottom: -25px;
    font-size: 13px;
  }
}