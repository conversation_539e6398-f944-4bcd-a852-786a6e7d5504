/* Notice Styles - Based on News.css */
.notice-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.notice-card {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.notice-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #1976D2;
  color: white;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  min-height: 56px;
  width: 100%;
  box-sizing: border-box;
}

.notice-header h1 {
  font-size: 18px;
  margin: 0;
  font-weight: 500;
  text-align: center;
  flex: 1;
  color: white;
}

.notice-back-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  transition: background-color 0.2s ease;
}

.notice-back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.notice-header-actions {
  display: flex;
  gap: 8px;
}

.admin-btn,
.filter-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  transition: background-color 0.2s ease;
}

.admin-btn:hover,
.filter-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.notice-search-container {
  background: white;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.notice-search-wrapper {
  position: relative;
  width: 100%;
}

.notice-search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 1px solid #ddd;
  border-radius: 24px;
  font-size: 14px;
  background: #f8f9fa;
  outline: none;
  transition: border-color 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
}

.notice-search-input:focus {
  border-color: #1976D2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
  background: white;
}

.notice-search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  pointer-events: none;
}

.notice-province-container {
  background: white;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  position: relative;
}

.notice-province-select {
  width: 100%;
  padding: 12px 40px 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  background: #f8f9fa;
  outline: none;
  cursor: pointer;
  appearance: none;
  transition: border-color 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
}

.notice-province-select:focus {
  border-color: #1976D2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
  background: white;
}

.province-dropdown-icon {
  position: absolute;
  right: 28px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  pointer-events: none;
}

.notice-categories-container {
  background: white;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  overflow: hidden;
}

.notice-categories-scroll {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 8px;
  scrollbar-width: thin;
  white-space: nowrap;
}

.notice-categories-scroll::-webkit-scrollbar {
  height: 4px;
}

.notice-categories-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.notice-categories-scroll::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.category-btn {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 16px;
  background: #f5f5f5;
  color: #666;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex-shrink: 0;
  width: auto;
  display: inline-block;
}

.category-btn:hover {
  background: #e0e0e0;
}

.category-btn.active {
  background: #0088cc;
  color: white;
  border-color: #0088cc;
  box-shadow: 0 2px 4px rgba(0, 136, 204, 0.2);
}

.notice-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.notice-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1976D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.notice-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #d32f2f;
}

.retry-btn {
  margin-top: 16px;
  padding: 8px 16px;
  background: #1976D2;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: #1565C0;
}

.notice-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
  text-align: center;
}

.notice-list {
  padding: 0;
}

.notice-item {
  display: flex;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.2s ease;
  gap: 12px;
}

.notice-item:hover {
  background-color: #f8f9fa;
}

.notice-item:last-child {
  border-bottom: none;
}

.notice-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.notice-item-date {
  font-size: 12px;
  color: #666;
  font-weight: 400;
}

.notice-item-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.notice-item-preview {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.notice-item-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.notice-category,
.notice-province {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.notice-category {
  background: #e3f2fd;
  color: #1976D2;
}

.notice-province {
  background: #f3e5f5;
  color: #7b1fa2;
}

.notice-item-image {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f5f5;
}

.notice-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media (max-width: 768px) {
  .notice-container {
    padding: 8px;
  }

  .notice-card {
    min-height: calc(100vh - 16px);
  }

  .notice-search-container,
  .notice-province-container,
  .notice-categories-container {
    padding: 12px;
  }

  .notice-search-input,
  .notice-province-select {
    font-size: 16px;
  }

  .notice-item {
    padding: 12px;
  }

  .notice-item-image {
    width: 60px;
    height: 60px;
  }
}

@media (max-width: 480px) {
  .notice-header {
    padding: 8px 12px;
  }

  .notice-header h1 {
    font-size: 16px;
  }

  .notice-search-container,
  .notice-province-container,
  .notice-categories-container {
    padding: 8px;
  }
}

/* Notice Detail Styles */
.notice-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.notice-detail-card {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.notice-detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #1976D2;
  color: white;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  min-height: 56px;
  width: 100%;
  box-sizing: border-box;
}

.notice-detail-header h1 {
  font-size: 18px;
  margin: 0;
  font-weight: 500;
  text-align: center;
  flex: 1;
  color: white;
}

.notice-detail-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.notice-detail-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.notice-detail-date {
  font-size: 14px;
  color: #666;
  font-weight: 400;
}

.notice-detail-category {
  display: inline-block;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 12px;
  font-weight: 500;
  background: #e3f2fd;
  color: #1976D2;
  width: fit-content;
}

.notice-detail-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
  margin: 16px 0;
}

.notice-detail-image {
  width: 100%;
  max-height: 300px;
  border-radius: 8px;
  overflow: hidden;
  margin: 16px 0;
  background: #f5f5f5;
}

.notice-detail-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.notice-detail-text {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.notice-detail-stats {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.notice-stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #666;
}

.like-btn {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  font-size: 14px;
  color: #666;
}

.like-btn.liked {
  color: #e91e63;
}

@media (max-width: 768px) {
  .notice-detail-container {
    padding: 8px;
  }

  .notice-detail-content {
    padding: 16px;
  }

  .notice-detail-title {
    font-size: 20px;
  }

  .notice-detail-text {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .notice-detail-content {
    padding: 12px;
  }

  .notice-detail-title {
    font-size: 18px;
  }

  .notice-detail-text {
    font-size: 14px;
  }
}
