import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';
import FilterModal from './FilterModal';
import './Notice.css';

const Notice = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [notices, setNotices] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedProvince, setSelectedProvince] = useState('All Provinces');
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showFilterModal, setShowFilterModal] = useState(false);

  const noticeCategories = [
    'all',
    'promotion',
    'transfer notice',
    'directives',
    'rules',
    'exams',
    'order',
    'general notice',
    'law',
    'un notice',
    'deputation',
    'other notice(career)',
    'bipad notice',
    'public procurement',
    'ordinance',
    'procedure'
  ];

  const provinces = [
    'All Provinces',
    'Province 1',
    'Madhesh Province',
    'Bagmati Province',
    'Gandaki Province',
    'Lumbini Province',
    'Karnali Province',
    'Sudurpashchim Province'
  ];

  const fetchNotices = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (selectedCategory !== 'all') {
        params.append('category', selectedCategory);
      }
      
      if (selectedProvince !== 'All Provinces') {
        params.append('province', selectedProvince);
      }
      
      if (searchQuery.trim()) {
        params.append('search', searchQuery.trim());
      }
      
      const response = await axios.get(`http://localhost:5000/api/notice?${params.toString()}`, {
        withCredentials: true
      });
      
      setNotices(response.data.notices || []);
    } catch (err) {
      setError('Failed to fetch notices');
      console.error('Error fetching notices:', err);
    } finally {
      setLoading(false);
    }
  }, [selectedCategory, selectedProvince, searchQuery]);

  useEffect(() => {
    fetchNotices();
  }, [fetchNotices]);

  const handleBack = () => {
    navigate('/home');
  };

  const handleCategorySelect = (category) => {
    setSelectedCategory(category);
  };

  const handleProvinceChange = (e) => {
    setSelectedProvince(e.target.value);
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleFilterClick = () => {
    setShowFilterModal(true);
  };

  const handleFilterClose = () => {
    setShowFilterModal(false);
  };

  const handleFilterApply = () => {
    // Filter will be applied automatically due to useEffect dependencies
    setShowFilterModal(false);
  };

  const handleNoticeClick = (noticeId) => {
    navigate(`/notice/${noticeId}`);
  };

  const handleAdminPanel = () => {
    navigate('/admin/notice');
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    // Simple B.S. conversion (approximate)
    const bsYear = year + 57;
    return `${year}-${month}-${day} (${bsYear}-${month}-${day} B.S)`;
  };

  const getCategoryDisplayName = (category) => {
    if (category === 'all') return 'All';
    return category.split(' ').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  // Show all categories
  const visibleCategories = noticeCategories;

  return (
    <div className="notice-container">
      <div className="notice-card">
        <div className="notice-header">
          <button className="notice-back-btn" onClick={handleBack}>
            <svg width="24" height="24" viewBox="0 0 24 24">
              <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" fill="white"/>
            </svg>
          </button>
          <h1>Notice</h1>
          <div className="notice-header-actions">
            {user && user.role === 'admin' && (
              <button className="admin-btn" onClick={handleAdminPanel}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </button>
            )}
            <button className="filter-btn" onClick={handleFilterClick}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                <path d="M4.25 5.61C6.27 8.2 10 13 10 13v6c0 .55.45 1 1 1h2c.55 0 1-.45 1-1v-6s3.73-4.8 5.75-7.39C20.25 4.95 19.78 4 18.95 4H5.04c-.83 0-1.3.95-.79 1.61z"/>
              </svg>
            </button>
          </div>
        </div>

        <div className="notice-search-container">
          <div className="notice-search-wrapper">
            <input
              type="text"
              placeholder="Search"
              value={searchQuery}
              onChange={handleSearchChange}
              className="notice-search-input"
            />
            <svg className="notice-search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
        </div>

        <div className="notice-province-container">
          <select 
            value={selectedProvince} 
            onChange={handleProvinceChange}
            className="notice-province-select"
          >
            {provinces.map(province => (
              <option key={province} value={province}>
                {province}
              </option>
            ))}
          </select>
          <svg className="province-dropdown-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M7 10l5 5 5-5z" fill="currentColor"/>
          </svg>
        </div>

        <div className="notice-categories-container">
          <div className="notice-categories-scroll">
            {visibleCategories.map(category => (
              <button
                key={category}
                className={`category-btn ${selectedCategory === category ? 'active' : ''}`}
                onClick={() => handleCategorySelect(category)}
              >
                {getCategoryDisplayName(category)}
              </button>
            ))}
          </div>
        </div>

        <div className="notice-content">
          {loading ? (
            <div className="notice-loading">
              <div className="loading-spinner"></div>
              <p>Loading notices...</p>
            </div>
          ) : error ? (
            <div className="notice-error">
              <p>{error}</p>
              <button onClick={fetchNotices} className="retry-btn">Retry</button>
            </div>
          ) : notices.length === 0 ? (
            <div className="notice-empty">
              <p>No notices found for the selected criteria.</p>
            </div>
          ) : (
            <div className="notice-list">
              {notices.map((item) => (
                <div 
                  key={item._id} 
                  className="notice-item"
                  onClick={() => handleNoticeClick(item._id)}
                >
                  <div className="notice-item-content">
                    <div className="notice-item-date">
                      {formatDate(item.createdAt)}
                    </div>
                    <div className="notice-item-title">
                      {item.title}
                    </div>
                    {item.content && (
                      <div className="notice-item-preview">
                        {item.content.substring(0, 100)}...
                      </div>
                    )}
                    <div className="notice-item-meta">
                      <span className="notice-category">{getCategoryDisplayName(item.category)}</span>
                      {item.province !== 'All Provinces' && (
                        <span className="notice-province">{item.province}</span>
                      )}
                    </div>
                  </div>
                  {item.image && (
                    <div className="notice-item-image">
                      <img 
                        src={`http://localhost:5000${item.image.url}`} 
                        alt={item.title}
                        onError={(e) => {
                          e.target.style.display = 'none';
                        }}
                      />
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <FilterModal
        isOpen={showFilterModal}
        onClose={handleFilterClose}
        selectedCategory={selectedCategory}
        selectedProvince={selectedProvince}
        onCategoryChange={setSelectedCategory}
        onProvinceChange={setSelectedProvince}
        onApply={handleFilterApply}
      />
    </div>
  );
};

export default Notice;
