import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import axios from 'axios';
import './Notice.css';

const NoticeDetail = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [notice, setNotice] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [liked, setLiked] = useState(false);

  const fetchNoticeDetail = useCallback(async () => {
    try {
      setLoading(true);
      const response = await axios.get(`http://localhost:5000/api/notice/${id}`, {
        withCredentials: true
      });
      setNotice(response.data);
    } catch (err) {
      setError('Failed to fetch notice details');
      console.error('Error fetching notice detail:', err);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    if (id) {
      fetchNoticeDetail();
    }
  }, [id, fetchNoticeDetail]);

  const handleBack = () => {
    navigate('/notice');
  };

  const handleLike = async () => {
    try {
      const response = await axios.post(`http://localhost:5000/api/notice/${id}/like`, {}, {
        withCredentials: true
      });
      setNotice(prev => ({
        ...prev,
        likes: response.data.likes
      }));
      setLiked(true);
    } catch (err) {
      console.error('Error liking notice:', err);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    // Simple B.S. conversion (approximate)
    const bsYear = year + 57;
    return `${year}-${month}-${day} (${bsYear}-${month}-${day} B.S)`;
  };

  const getCategoryDisplayName = (category) => {
    return category.split(' ').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  if (loading) {
    return (
      <div className="notice-detail-container">
        <div className="notice-detail-card">
          <div className="notice-detail-header">
            <button className="notice-back-btn" onClick={handleBack}>
              <svg width="24" height="24" viewBox="0 0 24 24">
                <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" fill="white"/>
              </svg>
            </button>
            <h1>Notice Detail</h1>
            <div style={{ width: '40px', height: '40px' }}></div>
          </div>
          <div className="notice-detail-content">
            <div className="notice-loading">
              <div className="loading-spinner"></div>
              <p>Loading notice...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !notice) {
    return (
      <div className="notice-detail-container">
        <div className="notice-detail-card">
          <div className="notice-detail-header">
            <button className="notice-back-btn" onClick={handleBack}>
              <svg width="24" height="24" viewBox="0 0 24 24">
                <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" fill="white"/>
              </svg>
            </button>
            <h1>Notice Detail</h1>
            <div style={{ width: '40px', height: '40px' }}></div>
          </div>
          <div className="notice-detail-content">
            <div className="notice-error">
              <p>{error || 'Notice not found'}</p>
              <button onClick={fetchNoticeDetail} className="retry-btn">Retry</button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="notice-detail-container">
      <div className="notice-detail-card">
        <div className="notice-detail-header">
          <button className="notice-back-btn" onClick={handleBack}>
            <svg width="24" height="24" viewBox="0 0 24 24">
              <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" fill="white"/>
            </svg>
          </button>
          <h1>Notice Detail</h1>
          <div style={{ width: '40px', height: '40px' }}></div>
        </div>

        <div className="notice-detail-content">
          <div className="notice-detail-meta">
            <div className="notice-detail-date">
              {formatDate(notice.createdAt)}
            </div>
            <div className="notice-detail-category">
              {getCategoryDisplayName(notice.category)}
            </div>
          </div>

          <h2 className="notice-detail-title">{notice.title}</h2>

          {notice.image && (
            <div className="notice-detail-image">
              <img 
                src={`http://localhost:5000${notice.image.url}`} 
                alt={notice.title}
                onError={(e) => {
                  e.target.parentElement.style.display = 'none';
                }}
              />
            </div>
          )}

          <div className="notice-detail-text">
            {notice.content}
          </div>

          <div className="notice-detail-stats">
            <button 
              className={`like-btn ${liked ? 'liked' : ''}`}
              onClick={handleLike}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill={liked ? 'currentColor' : 'none'} stroke="currentColor">
                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
              </svg>
              {notice.likes || 0}
            </button>

            <div className="notice-stat">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                <circle cx="12" cy="12" r="3"/>
              </svg>
              {notice.views || 0} views
            </div>

            {notice.createdBy && (
              <div className="notice-stat">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                  <circle cx="12" cy="7" r="4"/>
                </svg>
                By {notice.createdBy.name}
              </div>
            )}

            {notice.province && notice.province !== 'All Provinces' && (
              <div className="notice-stat">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                  <circle cx="12" cy="10" r="3"/>
                </svg>
                {notice.province}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NoticeDetail;
