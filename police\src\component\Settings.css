/* Settings Page Styles */
.settings-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.settings-card {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  background: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.settings-header {
  padding: 20px 16px 16px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
}

.settings-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.settings-content {
  flex: 1;
  padding: 0 0 80px 0;
  overflow-y: auto;
}

/* User Profile Section */
.user-profile-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 16px;
  border-bottom: 1px solid #e0e0e0;
}

.user-profile-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.profile-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.user-details h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 4px 0;
}

.user-details p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.edit-profile-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Settings Sections */
.settings-section {
  padding: 20px 0 0 0;
}

.settings-section h2 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 16px;
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  gap: 12px;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-icon {
  width: 24px;
  height: 24px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.setting-label {
  flex: 1;
  font-size: 16px;
  color: #333;
  font-weight: 400;
}

.setting-value {
  font-size: 14px;
  color: #666;
}

/* Toggle Switch */
.setting-toggle {
  margin-left: auto;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s ease;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s ease;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #1976D2;
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* Panic Mode Section */
.panic-mode-section {
  background-color: #f8f9fa;
  padding: 16px;
  margin: 20px 0;
}

.panic-mode-section .setting-item {
  padding: 0 0 12px 0;
  border-bottom: none;
}

.panic-mode-note {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin: 0;
}

/* Logout Section */
.logout-section {
  padding: 20px 16px;
}

.logout-btn {
  width: 100%;
  background: #d32f2f;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.2s ease;
}

.logout-btn:hover {
  background: #c62828;
}

/* Bottom Navigation */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 400px;
  background: #1976D2;
  display: flex;
  justify-content: space-around;
  padding: 8px 0;
  border-top: 1px solid #1565C0;
}

.nav-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border-radius: 8px;
  transition: none; /* Remove all transitions */
  flex: 1;
  max-width: 80px;
}

/* Remove all hover effects */
.nav-btn:hover {
  /* No hover effects */
}

.nav-btn.active {
  color: #FFD700;
}

.nav-btn img {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
  opacity: 0.7;
}

.nav-btn.active img {
  opacity: 1;
  filter: brightness(0) invert(1) sepia(1) saturate(10) hue-rotate(45deg);
}

.nav-btn span {
  font-size: 12px;
  font-weight: 400;
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .settings-card {
    max-width: 100%;
  }
  
  .bottom-nav {
    max-width: 100%;
  }
  
  .user-profile-section {
    padding: 16px;
  }
  
  .profile-avatar {
    width: 40px;
    height: 40px;
  }
  
  .user-details h3 {
    font-size: 16px;
  }
  
  .user-details p {
    font-size: 13px;
  }
  
  .setting-item {
    padding: 14px 16px;
  }
  
  .setting-label {
    font-size: 15px;
  }
}
