/* Settings Page Styles */
.settings-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.settings-card {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  background: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.settings-header {
  padding: 20px 16px 16px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
}

.settings-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.settings-content {
  flex: 1;
  padding: 0 0 80px 0;
  overflow-y: auto;
}

/* User Profile Section */
.user-profile-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 16px;
  border-bottom: 1px solid #e0e0e0;
}

.user-profile-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.profile-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.user-details h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 4px 0;
}

.user-details p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.edit-profile-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Settings Sections */
.settings-section {
  padding: 20px 0 0 0;
}

.settings-section h2 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 16px;
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  gap: 12px;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-icon {
  width: 24px;
  height: 24px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.setting-label {
  flex: 1;
  font-size: 16px;
  color: #333;
  font-weight: 400;
}

.setting-value {
  font-size: 14px;
  color: #666;
}

/* Toggle Switch */
.setting-toggle {
  margin-left: auto;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s ease;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s ease;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #1976D2;
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* Panic Mode Section */
.panic-mode-section {
  background-color: #f8f9fa;
  padding: 16px;
  margin: 20px 0;
}

.panic-mode-section .setting-item {
  padding: 0 0 12px 0;
  border-bottom: none;
}

.panic-mode-note {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin: 0;
}

/* Logout Section */
.logout-section {
  padding: 20px 16px;
}

.logout-btn {
  width: 100%;
  background: #d32f2f;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.2s ease;
}

.logout-btn:hover {
  background: #c62828;
}

/* Bottom Navigation - Exact Match to Screenshot */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 400px;
  background: #1976D2;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 12px 20px;
  border-radius: 20px 20px 0 0;
  z-index: 1000;
  height: 70px;
}

.nav-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 8px 16px;
  transition: none;
  flex: 1;
  max-width: 80px;
}

/* Remove all hover effects */
.nav-btn:hover {
  /* No hover effects */
}

.nav-btn.active {
  color: #FFD700;
}

.nav-btn.active span {
  color: #FFD700;
}

.nav-btn img {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
  opacity: 0.7;
}

.nav-btn.active img {
  opacity: 1;
  filter: brightness(0) invert(1) sepia(1) saturate(10) hue-rotate(45deg);
}

.nav-btn span {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
  margin-top: 2px;
}

/* Special styling for Public Eye button (middle button) */
.nav-btn:nth-child(2) {
  background: #0088cc;
  width: 60px;
  height: 60px;
  padding: 0;
  border-radius: 50%;
  margin-bottom: 30px;
  transform: translateY(-15px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  position: relative;
  flex: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:nth-child(2) img {
  width: 28px;
  height: 28px;
  filter: brightness(0) invert(1);
  opacity: 1;
}

.nav-btn:nth-child(2) span {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
}

/* Responsive Design for Bottom Navigation */
@media (max-width: 480px) {
  .settings-card {
    max-width: 100%;
  }

  .bottom-nav {
    max-width: 100%;
    padding: 10px 16px;
    height: 65px;
  }

  .nav-btn {
    padding: 6px 12px;
    max-width: 70px;
  }

  .nav-btn img {
    width: 22px;
    height: 22px;
  }

  .nav-btn span {
    font-size: 11px;
  }

  .nav-btn:nth-child(2) {
    width: 55px;
    height: 55px;
    margin-bottom: 25px;
    transform: translateY(-12px);
  }

  .nav-btn:nth-child(2) img {
    width: 26px;
    height: 26px;
  }

  .nav-btn:nth-child(2) span {
    bottom: -22px;
    font-size: 11px;
  }

  .user-profile-section {
    padding: 16px;
  }

  .profile-avatar {
    width: 40px;
    height: 40px;
  }

  .user-details h3 {
    font-size: 16px;
  }

  .user-details p {
    font-size: 13px;
  }

  .setting-item {
    padding: 14px 16px;
  }

  .setting-label {
    font-size: 15px;
  }
}

@media (min-width: 768px) {
  .bottom-nav {
    max-width: 500px;
    padding: 15px 25px;
    height: 75px;
    border-radius: 25px 25px 0 0;
  }

  .nav-btn {
    padding: 10px 18px;
    max-width: 90px;
  }

  .nav-btn img {
    width: 26px;
    height: 26px;
  }

  .nav-btn span {
    font-size: 13px;
  }

  .nav-btn:nth-child(2) {
    width: 65px;
    height: 65px;
    margin-bottom: 35px;
    transform: translateY(-18px);
  }

  .nav-btn:nth-child(2) img {
    width: 30px;
    height: 30px;
  }

  .nav-btn:nth-child(2) span {
    bottom: -28px;
    font-size: 13px;
  }
}

@media (min-width: 1024px) {
  .bottom-nav {
    max-width: 600px;
    padding: 18px 30px;
    height: 80px;
  }

  .nav-btn {
    padding: 12px 20px;
    max-width: 100px;
  }

  .nav-btn img {
    width: 28px;
    height: 28px;
  }

  .nav-btn span {
    font-size: 14px;
  }

  .nav-btn:nth-child(2) {
    width: 70px;
    height: 70px;
    margin-bottom: 40px;
    transform: translateY(-20px);
  }

  .nav-btn:nth-child(2) img {
    width: 32px;
    height: 32px;
  }

  .nav-btn:nth-child(2) span {
    bottom: -30px;
    font-size: 14px;
  }
}
